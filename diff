[2025-07-10T10:42:47.894508+00:00] report.DEBUG: Source class "\Magento\CommerceBackendUix\Model\Composite" for "Magento\CommerceBackendUix\Model\CompositeLogger" generation does not exist. {"exception":"[object] (RuntimeException(code: 0): Source class \"\\Magento\\CommerceBackendUix\\Model\\Composite\" for \"Magento\\CommerceBackendUix\\Model\\CompositeLogger\" generation does not exist. at /var/www/html/vendor/magento/framework/Code/Generator.php:223)"} []
[2025-07-10T10:42:47.928247+00:00] report.DEBUG: Source class "\Magento\CommerceBackendUix\Model\Composite" for "Magento\CommerceBackendUix\Model\CompositeLogger" generation does not exist. {"exception":"[object] (RuntimeException(code: 0): Source class \"\\Magento\\CommerceBackendUix\\Model\\Composite\" for \"Magento\\CommerceBackendUix\\Model\\CompositeLogger\" generation does not exist. at /var/www/html/vendor/magento/framework/Code/Generator.php:223)"} []
[2025-07-10T10:42:49.371063+00:00] report.DEBUG: Source class "\Magento\CommerceBackendUix\Model\Composite" for "Magento\CommerceBackendUix\Model\CompositeLogger" generation does not exist. {"exception":"[object] (RuntimeException(code: 0): Source class \"\\Magento\\CommerceBackendUix\\Model\\Composite\" for \"Magento\\CommerceBackendUix\\Model\\CompositeLogger\" generation does not exist. at /var/www/html/vendor/magento/framework/Code/Generator.php:223)"} []
[2025-07-10T10:42:49.440270+00:00] report.DEBUG: Source class "\Magento\CommerceBackendUix\Model\Composite" for "Magento\CommerceBackendUix\Model\CompositeLogger" generation does not exist. {"exception":"[object] (RuntimeException(code: 0): Source class \"\\Magento\\CommerceBackendUix\\Model\\Composite\" for \"Magento\\CommerceBackendUix\\Model\\CompositeLogger\" generation does not exist. at /var/www/html/vendor/magento/framework/Code/Generator.php:223)"} []
[2025-07-10T10:42:59.096804+00:00] report.INFO: GraphQL: About to save category image data {"category_id":"683","attribute_name":"vertical_image","image_path":"/c/a/category-image1.png","image_url":"https://comave-magento.ddev.site/media/c/a/category-image1.png","current_category_data_keys":["entity_id","attribute_set_id","parent_id","created_at","updated_at","path","position","level","children_count","row_id","created_in","updated_in","weltpixel_mm_columns_number","weltpixel_mm_column_width","description","meta_keywords","meta_description","weltpixel_category_url","weltpixel_mm_top_block","weltpixel_mm_right_block","weltpixel_mm_bottom_block","weltpixel_mm_left_block","weltpixel_mm_font_color","weltpixel_mm_font_hover_color","weltpixel_mm_dynamic_sc_opts","weltpixel_mm_label_font_color","weltpixel_mm_label_background_color","weltpixel_mm_image_alt","weltpixel_mm_image_radius","children","name","display_mode","page_layout","url_key","url_path","commission_for_admin","weltpixel_mm_display_mode","weltpixel_mm_top_block_type","weltpixel_mm_right_block_type","weltpixel_mm_bottom_block_type","weltpixel_mm_left_block_type","weltpixel_sc_layout","weltpixel_sc_title_position","weltpixel_mm_image_name_align","weltpixel_mm_label_position","weltpixel_mm_image_position","image","meta_title","custom_design","weltpixel_mm_top_block_cms","weltpixel_mm_right_block_cms","weltpixel_mm_bottom_block_cms","weltpixel_mm_left_block_cms","weltpixel_sc_image","weltpixel_mm_image","weltpixel_mm_label_text","wp_canonical_url","title_rewrite","is_active","is_anchor","include_in_menu","custom_use_parent_settings","custom_apply_to_products","weltpixel_category_url_newtab","weltpixel_mm_mob_hide_allcat","weltpixel_mm_show_arrows","weltpixel_mm_dynamic_sc_flag","weltpixel_sc_columns","weltpixel_sc_hide","weltpixel_mm_image_enable","weltpixel_hide_title","weltpixel_hide_breadcrumbs","weltpixel_exclude_from_sitemap","wp_enable_index_follow","wp_index_value","wp_follow_value","wp_enable_canonical_url","landing_page","weltpixel_mm_image_width","weltpixel_mm_image_height","horizontal_image","commission_type","commission_value","extension_attributes"]} []
[2025-07-10T10:42:59.097246+00:00] report.INFO: GraphQL: Set category image data {"category_id":"683","attribute_name":"vertical_image","main_attribute_value":"/c/a/category-image1.png","additional_data_value":[{"file":"/c/a/category-image1.png","url":"https://comave-magento.ddev.site/media/c/a/category-image1.png","name":"category-image1.png","size":0,"type":"image/png"}],"all_category_data_keys":["entity_id","attribute_set_id","parent_id","created_at","updated_at","path","position","level","children_count","row_id","created_in","updated_in","weltpixel_mm_columns_number","weltpixel_mm_column_width","description","meta_keywords","meta_description","weltpixel_category_url","weltpixel_mm_top_block","weltpixel_mm_right_block","weltpixel_mm_bottom_block","weltpixel_mm_left_block","weltpixel_mm_font_color","weltpixel_mm_font_hover_color","weltpixel_mm_dynamic_sc_opts","weltpixel_mm_label_font_color","weltpixel_mm_label_background_color","weltpixel_mm_image_alt","weltpixel_mm_image_radius","children","name","display_mode","page_layout","url_key","url_path","commission_for_admin","weltpixel_mm_display_mode","weltpixel_mm_top_block_type","weltpixel_mm_right_block_type","weltpixel_mm_bottom_block_type","weltpixel_mm_left_block_type","weltpixel_sc_layout","weltpixel_sc_title_position","weltpixel_mm_image_name_align","weltpixel_mm_label_position","weltpixel_mm_image_position","image","meta_title","custom_design","weltpixel_mm_top_block_cms","weltpixel_mm_right_block_cms","weltpixel_mm_bottom_block_cms","weltpixel_mm_left_block_cms","weltpixel_sc_image","weltpixel_mm_image","weltpixel_mm_label_text","wp_canonical_url","title_rewrite","is_active","is_anchor","include_in_menu","custom_use_parent_settings","custom_apply_to_products","weltpixel_category_url_newtab","weltpixel_mm_mob_hide_allcat","weltpixel_mm_show_arrows","weltpixel_mm_dynamic_sc_flag","weltpixel_sc_columns","weltpixel_sc_hide","weltpixel_mm_image_enable","weltpixel_hide_title","weltpixel_hide_breadcrumbs","weltpixel_exclude_from_sitemap","wp_enable_index_follow","wp_index_value","wp_follow_value","wp_enable_canonical_url","landing_page","weltpixel_mm_image_width","weltpixel_mm_image_height","horizontal_image","commission_type","commission_value","extension_attributes","vertical_image","_additional_data_vertical_image"]} []
[2025-07-10T10:42:59.152560+00:00] report.DEBUG: cache_invalidate:  {"method":"POST","url":"https://comave-magento.ddev.site/graphql","invalidateInfo":{"tags":["cat_c_683","cat_c_p_683"],"mode":"matchingAnyTag"}} []
[2025-07-10T10:42:59.188877+00:00] report.DEBUG: cache_invalidate:  {"method":"POST","url":"https://comave-magento.ddev.site/graphql","invalidateInfo":{"tags":["cat_c_683"],"mode":"matchingAnyTag"}} []
[2025-07-10T10:42:59.188962+00:00] report.DEBUG: cache_invalidate:  {"method":"POST","url":"https://comave-magento.ddev.site/graphql","invalidateInfo":{"tags":["cat_c_683"],"mode":"matchingAnyTag"}} []
[2025-07-10T10:42:59.453560+00:00] report.INFO: GraphQL: Category saved successfully {"category_id":"683","attribute_name":"vertical_image","saved_main_attribute":"/c/a/category-image1.png","saved_additional_data":null,"all_saved_data_keys":["store_id","entity_id","attribute_set_id","parent_id","created_at","updated_at","path","position","level","children_count","row_id","created_in","updated_in","weltpixel_mm_columns_number","weltpixel_mm_column_width","description","meta_keywords","meta_description","weltpixel_category_url","weltpixel_mm_top_block","weltpixel_mm_right_block","weltpixel_mm_bottom_block","weltpixel_mm_left_block","weltpixel_mm_font_color","weltpixel_mm_font_hover_color","weltpixel_mm_dynamic_sc_opts","weltpixel_mm_label_font_color","weltpixel_mm_label_background_color","weltpixel_mm_image_alt","weltpixel_mm_image_radius","children","name","display_mode","page_layout","url_key","url_path","commission_for_admin","weltpixel_mm_display_mode","weltpixel_mm_top_block_type","weltpixel_mm_right_block_type","weltpixel_mm_bottom_block_type","weltpixel_mm_left_block_type","weltpixel_sc_layout","weltpixel_sc_title_position","weltpixel_mm_image_name_align","weltpixel_mm_label_position","weltpixel_mm_image_position","image","meta_title","custom_design","weltpixel_mm_top_block_cms","weltpixel_mm_right_block_cms","weltpixel_mm_bottom_block_cms","weltpixel_mm_left_block_cms","weltpixel_sc_image","weltpixel_mm_image","weltpixel_mm_label_text","wp_canonical_url","title_rewrite","is_active","is_anchor","include_in_menu","custom_use_parent_settings","custom_apply_to_products","weltpixel_category_url_newtab","weltpixel_mm_mob_hide_allcat","weltpixel_mm_show_arrows","weltpixel_mm_dynamic_sc_flag","weltpixel_sc_columns","weltpixel_sc_hide","weltpixel_mm_image_enable","weltpixel_hide_title","weltpixel_hide_breadcrumbs","weltpixel_exclude_from_sitemap","wp_enable_index_follow","wp_index_value","wp_follow_value","wp_enable_canonical_url","landing_page","weltpixel_mm_image_width","weltpixel_mm_image_height","horizontal_image","vertical_image","commission_type","commission_value","extension_attributes"]} []
[2025-07-10T10:42:59.459196+00:00] report.INFO: GraphQL: Reloaded category from database {"category_id":"683","attribute_name":"vertical_image","reloaded_main_attribute":"/c/a/category-image1.png","reloaded_additional_data":null,"all_reloaded_data_keys":["entity_id","attribute_set_id","parent_id","created_at","updated_at","path","position","level","children_count","row_id","created_in","updated_in","weltpixel_mm_columns_number","weltpixel_mm_column_width","description","meta_keywords","meta_description","weltpixel_category_url","weltpixel_mm_top_block","weltpixel_mm_right_block","weltpixel_mm_bottom_block","weltpixel_mm_left_block","weltpixel_mm_font_color","weltpixel_mm_font_hover_color","weltpixel_mm_dynamic_sc_opts","weltpixel_mm_label_font_color","weltpixel_mm_label_background_color","weltpixel_mm_image_alt","weltpixel_mm_image_radius","children","name","display_mode","page_layout","url_key","url_path","commission_for_admin","weltpixel_mm_display_mode","weltpixel_mm_top_block_type","weltpixel_mm_right_block_type","weltpixel_mm_bottom_block_type","weltpixel_mm_left_block_type","weltpixel_sc_layout","weltpixel_sc_title_position","weltpixel_mm_image_name_align","weltpixel_mm_label_position","weltpixel_mm_image_position","image","meta_title","custom_design","weltpixel_mm_top_block_cms","weltpixel_mm_right_block_cms","weltpixel_mm_bottom_block_cms","weltpixel_mm_left_block_cms","weltpixel_sc_image","weltpixel_mm_image","weltpixel_mm_label_text","wp_canonical_url","title_rewrite","is_active","is_anchor","include_in_menu","custom_use_parent_settings","custom_apply_to_products","weltpixel_category_url_newtab","weltpixel_mm_mob_hide_allcat","weltpixel_mm_show_arrows","weltpixel_mm_dynamic_sc_flag","weltpixel_sc_columns","weltpixel_sc_hide","weltpixel_mm_image_enable","weltpixel_hide_title","weltpixel_hide_breadcrumbs","weltpixel_exclude_from_sitemap","wp_enable_index_follow","wp_index_value","wp_follow_value","wp_enable_canonical_url","landing_page","weltpixel_mm_image_width","weltpixel_mm_image_height","horizontal_image","vertical_image","commission_type","commission_value","extension_attributes"]} []
[2025-07-10T10:43:23.762933+00:00] report.DEBUG: Source class "\Magento\CommerceBackendUix\Model\Composite" for "Magento\CommerceBackendUix\Model\CompositeLogger" generation does not exist. {"exception":"[object] (RuntimeException(code: 0): Source class \"\\Magento\\CommerceBackendUix\\Model\\Composite\" for \"Magento\\CommerceBackendUix\\Model\\CompositeLogger\" generation does not exist. at /var/www/html/vendor/magento/framework/Code/Generator.php:223)"} []
[2025-07-10T10:43:27.005525+00:00] report.INFO: Broken reference: the 'notification.messages' tries to reorder itself towards 'user', but their parents are different: 'header.inner.right' and 'header' respectively. [] []
[2025-07-10T10:43:32.004024+00:00] report.DEBUG: Item Magento_Cms::cms_page was removed [] []
[2025-07-10T10:43:32.004132+00:00] report.DEBUG: Item Magento_Backend::system_currency was removed [] []
[2025-07-10T10:43:32.004269+00:00] report.DEBUG: Item Magento_AsynchronousOperations::system_magento_logging was removed [] []
[2025-07-10T10:43:32.004308+00:00] report.DEBUG: Item Magento_AsynchronousOperations::system_magento_logging_bulk_operations was removed [] []
[2025-07-10T10:43:32.004364+00:00] report.DEBUG: Item Magento_LoginAsCustomerLog::login_log was removed [] []
[2025-07-10T10:43:32.008790+00:00] report.DEBUG: Add of item with id Magento_Config::system_config was processed [] []
[2025-07-10T10:43:32.008841+00:00] report.DEBUG: Add of item with id Magento_AdminNotification::system_adminnotification was processed [] []
[2025-07-10T10:43:32.008884+00:00] report.DEBUG: Add of item with id Magento_Theme::design_config was processed [] []
[2025-07-10T10:43:32.008919+00:00] report.DEBUG: Add of item with id Magento_Theme::system_design_theme was processed [] []
[2025-07-10T10:43:32.008957+00:00] report.DEBUG: Add of item with id Magento_AdobeCommerceWebhooks::menu was processed [] []
[2025-07-10T10:43:32.008996+00:00] report.DEBUG: Add of item with id Magento_AdobeCommerceWebhooks::webhooks_logs was processed [] []
[2025-07-10T10:43:32.009030+00:00] report.DEBUG: Add of item with id Magento_AdobeCommerceEventsClient::menu was processed [] []
[2025-07-10T10:43:32.009068+00:00] report.DEBUG: Add of item with id Magento_AdobeCommerceEventsClient::event_list was processed [] []
[2025-07-10T10:43:32.009101+00:00] report.DEBUG: Add of item with id Magento_AdobeCommerceEventsClient::event_status was processed [] []
[2025-07-10T10:43:32.009135+00:00] report.DEBUG: Add of item with id Magento_AdobeCommerceEventsClient::event_provider was processed [] []
[2025-07-10T10:43:32.009168+00:00] report.DEBUG: Add of item with id Magento_Variable::system_variable was processed [] []
[2025-07-10T10:43:32.009202+00:00] report.DEBUG: Add of item with id Magento_Customer::customer was processed [] []
[2025-07-10T10:43:32.009239+00:00] report.DEBUG: Add of item with id Magento_Customer::customer_manage was processed [] []
[2025-07-10T10:43:32.009273+00:00] report.DEBUG: Add of item with id Magento_Customer::customer_online was processed [] []
[2025-07-10T10:43:32.009306+00:00] report.DEBUG: Add of item with id Magento_Customer::customer_group was processed [] []
[2025-07-10T10:43:32.009343+00:00] report.DEBUG: Add of item with id Magento_Indexer::system_index was processed [] []
[2025-07-10T10:43:32.009380+00:00] report.DEBUG: Add of item with id Magento_Cms::cms_block was processed [] []
[2025-07-10T10:43:32.009414+00:00] report.DEBUG: Add of item with id Magento_Backend::system_design_schedule was processed [] []
[2025-07-10T10:43:32.009457+00:00] report.DEBUG: Add of item with id Magento_Backend::system_store was processed [] []
[2025-07-10T10:43:32.009490+00:00] report.DEBUG: Add of item with id Magento_Backend::dashboard was processed [] []
[2025-07-10T10:43:32.009524+00:00] report.DEBUG: Add of item with id Magento_Backend::system was processed [] []
[2025-07-10T10:43:32.009557+00:00] report.DEBUG: Add of item with id Magento_Backend::system_tools was processed [] []
[2025-07-10T10:43:32.009594+00:00] report.DEBUG: Add of item with id Magento_Backend::system_design was processed [] []
[2025-07-10T10:43:32.009627+00:00] report.DEBUG: Add of item with id Magento_Backend::system_convert was processed [] []
[2025-07-10T10:43:32.009660+00:00] report.DEBUG: Add of item with id Magento_Backend::system_cache was processed [] []
[2025-07-10T10:43:32.009693+00:00] report.DEBUG: Add of item with id Magento_Backend::marketing was processed [] []
[2025-07-10T10:43:32.009730+00:00] report.DEBUG: Add of item with id Magento_Backend::marketing_communications was processed [] []
[2025-07-10T10:43:32.009763+00:00] report.DEBUG: Add of item with id Magento_Backend::marketing_seo was processed [] []
[2025-07-10T10:43:32.009796+00:00] report.DEBUG: Add of item with id Magento_Backend::marketing_user_content was processed [] []
[2025-07-10T10:43:32.009829+00:00] report.DEBUG: Add of item with id Magento_Backend::content was processed [] []
[2025-07-10T10:43:32.009863+00:00] report.DEBUG: Add of item with id Magento_Backend::content_elements was processed [] []
[2025-07-10T10:43:32.009896+00:00] report.DEBUG: Add of item with id Magento_Backend::stores was processed [] []
[2025-07-10T10:43:32.009936+00:00] report.DEBUG: Add of item with id Magento_Backend::stores_settings was processed [] []
[2025-07-10T10:43:32.009970+00:00] report.DEBUG: Add of item with id Magento_Backend::stores_attributes was processed [] []
[2025-07-10T10:43:32.010003+00:00] report.DEBUG: Add of item with id Magento_Backend::other_settings was processed [] []
[2025-07-10T10:43:32.010035+00:00] report.DEBUG: Add of item with id Magento_Backend::system_other_settings was processed [] []
[2025-07-10T10:43:32.010069+00:00] report.DEBUG: Add of item with id Magento_Catalog::catalog was processed [] []
[2025-07-10T10:43:32.010106+00:00] report.DEBUG: Add of item with id Magento_Catalog::catalog_products was processed [] []
[2025-07-10T10:43:32.010141+00:00] report.DEBUG: Add of item with id Magento_Catalog::catalog_categories was processed [] []
[2025-07-10T10:43:32.010179+00:00] report.DEBUG: Add of item with id Magento_Catalog::catalog_attributes_attributes was processed [] []
[2025-07-10T10:43:32.010213+00:00] report.DEBUG: Add of item with id Magento_Catalog::catalog_attributes_sets was processed [] []
[2025-07-10T10:43:32.010250+00:00] report.DEBUG: Add of item with id Magento_Catalog::inventory was processed [] []
[2025-07-10T10:43:32.010283+00:00] report.DEBUG: Add of item with id Magento_User::system_acl was processed [] []
[2025-07-10T10:43:32.010320+00:00] report.DEBUG: Add of item with id Magento_User::system_acl_users was processed [] []
[2025-07-10T10:43:32.010354+00:00] report.DEBUG: Add of item with id Magento_User::system_acl_roles was processed [] []
[2025-07-10T10:43:32.010390+00:00] report.DEBUG: Add of item with id Magento_User::system_acl_locks was processed [] []
[2025-07-10T10:43:32.010430+00:00] report.DEBUG: Add of item with id Magento_Widget::cms_widget_instance was processed [] []
[2025-07-10T10:43:32.010470+00:00] report.DEBUG: Add of item with id Magento_ImportExport::system_convert_import was processed [] []
[2025-07-10T10:43:32.010503+00:00] report.DEBUG: Add of item with id Magento_ImportExport::system_convert_export was processed [] []
[2025-07-10T10:43:32.010536+00:00] report.DEBUG: Add of item with id Magento_ImportExport::system_convert_history was processed [] []
[2025-07-10T10:43:32.010569+00:00] report.DEBUG: Add of item with id Magento_Enterprise::private_sales was processed [] []
[2025-07-10T10:43:32.010603+00:00] report.DEBUG: Add of item with id Magento_Backup::system_tools_backup was processed [] []
[2025-07-10T10:43:32.010636+00:00] report.DEBUG: Add of item with id Magento_CatalogRule::promo was processed [] []
[2025-07-10T10:43:32.010673+00:00] report.DEBUG: Add of item with id Magento_CatalogRule::promo_catalog was processed [] []
[2025-07-10T10:43:32.010706+00:00] report.DEBUG: Add of item with id Magento_Sales::sales was processed [] []
[2025-07-10T10:43:32.010744+00:00] report.DEBUG: Add of item with id Magento_Sales::sales_operation was processed [] []
[2025-07-10T10:43:32.010781+00:00] report.DEBUG: Add of item with id Magento_Sales::sales_order was processed [] []
[2025-07-10T10:43:32.010814+00:00] report.DEBUG: Add of item with id Magento_Sales::sales_invoice was processed [] []
[2025-07-10T10:43:32.010847+00:00] report.DEBUG: Add of item with id Magento_Sales::sales_shipment was processed [] []
[2025-07-10T10:43:32.010880+00:00] report.DEBUG: Add of item with id Magento_Sales::sales_creditmemo was processed [] []
[2025-07-10T10:43:32.010913+00:00] report.DEBUG: Add of item with id Magento_Sales::sales_transactions was processed [] []
[2025-07-10T10:43:32.010946+00:00] report.DEBUG: Add of item with id Magento_Sales::system_order_statuses was processed [] []
[2025-07-10T10:43:32.010979+00:00] report.DEBUG: Add of item with id Magento_SalesRule::promo_quote was processed [] []
[2025-07-10T10:43:32.011016+00:00] report.DEBUG: Add of item with id Magento_Search::search_terms was processed [] []
[2025-07-10T10:43:32.011050+00:00] report.DEBUG: Add of item with id Magento_Search::search_synonyms was processed [] []
[2025-07-10T10:43:32.011083+00:00] report.DEBUG: Add of item with id Magento_Integration::system_extensions was processed [] []
[2025-07-10T10:43:32.011133+00:00] report.DEBUG: Add of item with id Magento_Integration::system_integrations was processed [] []
[2025-07-10T10:43:32.011170+00:00] report.DEBUG: Add of item with id Magento_CommerceBackendUix::adminuisdk_management was processed [] []
[2025-07-10T10:43:32.011207+00:00] report.DEBUG: Add of item with id Magento_CommerceBackendUix::adminuisdk_log_grid was processed [] []
[2025-07-10T10:43:32.011241+00:00] report.DEBUG: Add of item with id Magento_CustomerCustomAttributes::customer_attributes_customer_attributes was processed [] []
[2025-07-10T10:43:32.011275+00:00] report.DEBUG: Add of item with id Magento_CustomerCustomAttributes::customer_attributes_customer_address_attributes was processed [] []
[2025-07-10T10:43:32.011308+00:00] report.DEBUG: Add of item with id Magento_VersionsCms::versionscms_page_page was processed [] []
[2025-07-10T10:43:32.011342+00:00] report.DEBUG: Add of item with id Magento_VersionsCms::versionscms_page_hierarchy was processed [] []
[2025-07-10T10:43:32.011379+00:00] report.DEBUG: Add of item with id Magento_Downloadable::report_products_downloads was processed [] []
[2025-07-10T10:43:32.011412+00:00] report.DEBUG: Add of item with id Magento_Backend::content_staging was processed [] []
[2025-07-10T10:43:32.011459+00:00] report.DEBUG: Add of item with id Magento_Staging::staging was processed [] []
[2025-07-10T10:43:32.011493+00:00] report.DEBUG: Add of item with id Magento_UrlRewrite::urlrewrite was processed [] []
[2025-07-10T10:43:32.011530+00:00] report.DEBUG: Add of item with id Magento_GiftRegistry::customer_magento_giftregistry was processed [] []
[2025-07-10T10:43:32.011564+00:00] report.DEBUG: Add of item with id Magento_CheckoutAgreements::sales_checkoutagreement was processed [] []
[2025-07-10T10:43:32.011597+00:00] report.DEBUG: Add of item with id Magento_CurrencySymbol::system_currency was processed [] []
[2025-07-10T10:43:32.011634+00:00] report.DEBUG: Add of item with id Magento_CurrencySymbol::system_currency_rates was processed [] []
[2025-07-10T10:43:32.011668+00:00] report.DEBUG: Add of item with id Magento_CurrencySymbol::system_currency_symbols was processed [] []
[2025-07-10T10:43:32.011705+00:00] report.DEBUG: Add of item with id Magento_Analytics::business_intelligence was processed [] []
[2025-07-10T10:43:32.011742+00:00] report.DEBUG: Add of item with id Magento_Analytics::advanced_reporting was processed [] []
[2025-07-10T10:43:32.011776+00:00] report.DEBUG: Add of item with id Magento_Analytics::bi_essentials was processed [] []
[2025-07-10T10:43:32.011809+00:00] report.DEBUG: Add of item with id Magento_CustomerSegment::customer_customersegment was processed [] []
[2025-07-10T10:43:32.011846+00:00] report.DEBUG: Add of item with id Magento_CustomerSegment::report_customers_segment was processed [] []
[2025-07-10T10:43:32.011883+00:00] report.DEBUG: Add of item with id Magento_Newsletter::newsletter_template was processed [] []
[2025-07-10T10:43:32.011917+00:00] report.DEBUG: Add of item with id Magento_Newsletter::newsletter_queue was processed [] []
[2025-07-10T10:43:32.011950+00:00] report.DEBUG: Add of item with id Magento_Newsletter::newsletter_subscriber was processed [] []
[2025-07-10T10:43:32.011987+00:00] report.DEBUG: Add of item with id Magento_Newsletter::newsletter_problem was processed [] []
[2025-07-10T10:43:32.012021+00:00] report.DEBUG: Add of item with id Magento_TargetRule::catalog_targetrule was processed [] []
[2025-07-10T10:43:32.012054+00:00] report.DEBUG: Add of item with id Magento_Tax::sales_tax was processed [] []
[2025-07-10T10:43:32.012091+00:00] report.DEBUG: Add of item with id Magento_Tax::sales_tax_rules was processed [] []
[2025-07-10T10:43:32.012124+00:00] report.DEBUG: Add of item with id Magento_Tax::sales_tax_rates was processed [] []
[2025-07-10T10:43:32.012158+00:00] report.DEBUG: Add of item with id Magento_Email::template was processed [] []
[2025-07-10T10:43:32.012191+00:00] report.DEBUG: Add of item with id Magento_EncryptionKey::system_crypt_key was processed [] []
[2025-07-10T10:43:32.012230+00:00] report.DEBUG: Add of item with id Magento_CatalogEvent::catalog_magento_catalogevent_events was processed [] []
[2025-07-10T10:43:32.012264+00:00] report.DEBUG: Add of item with id Magento_GiftCardAccount::customer_giftcardaccount was processed [] []
[2025-07-10T10:43:32.012297+00:00] report.DEBUG: Add of item with id Magento_Sitemap::catalog_sitemap was processed [] []
[2025-07-10T10:43:32.012330+00:00] report.DEBUG: Add of item with id Magento_GiftWrapping::sales_magento_giftwrapping was processed [] []
[2025-07-10T10:43:32.012363+00:00] report.DEBUG: Add of item with id Magento_Banner::cms_magento_banner was processed [] []
[2025-07-10T10:43:32.012396+00:00] report.DEBUG: Add of item with id Magento_InventoryAdminUi::inventory was processed [] []
[2025-07-10T10:43:32.012440+00:00] report.DEBUG: Add of item with id Magento_InventoryAdminUi::source was processed [] []
[2025-07-10T10:43:32.012475+00:00] report.DEBUG: Add of item with id Magento_InventoryAdminUi::stock was processed [] []
[2025-07-10T10:43:32.012508+00:00] report.DEBUG: Add of item with id Magento_Reports::report was processed [] []
[2025-07-10T10:43:32.012541+00:00] report.DEBUG: Add of item with id Magento_Reports::report_marketing was processed [] []
[2025-07-10T10:43:32.012574+00:00] report.DEBUG: Add of item with id Magento_Reports::report_salesroot was processed [] []
[2025-07-10T10:43:32.012611+00:00] report.DEBUG: Add of item with id Magento_Reports::report_salesroot_sales was processed [] []
[2025-07-10T10:43:32.012645+00:00] report.DEBUG: Add of item with id Magento_Reports::report_salesroot_tax was processed [] []
[2025-07-10T10:43:32.012678+00:00] report.DEBUG: Add of item with id Magento_Reports::report_salesroot_invoiced was processed [] []
[2025-07-10T10:43:32.012711+00:00] report.DEBUG: Add of item with id Magento_Reports::report_salesroot_shipping was processed [] []
[2025-07-10T10:43:32.012743+00:00] report.DEBUG: Add of item with id Magento_Reports::report_salesroot_refunded was processed [] []
[2025-07-10T10:43:32.012776+00:00] report.DEBUG: Add of item with id Magento_Reports::report_salesroot_coupons was processed [] []
[2025-07-10T10:43:32.012809+00:00] report.DEBUG: Add of item with id Magento_Reports::report_shopcart_product was processed [] []
[2025-07-10T10:43:32.012842+00:00] report.DEBUG: Add of item with id Magento_Reports::report_shopcart_abandoned was processed [] []
[2025-07-10T10:43:32.012876+00:00] report.DEBUG: Add of item with id Magento_Reports::report_products was processed [] []
[2025-07-10T10:43:32.012910+00:00] report.DEBUG: Add of item with id Magento_Reports::report_products_bestsellers was processed [] []
[2025-07-10T10:43:32.012964+00:00] report.DEBUG: Add of item with id Magento_Reports::report_products_sold was processed [] []
[2025-07-10T10:43:32.013003+00:00] report.DEBUG: Add of item with id Magento_Reports::report_products_viewed was processed [] []
[2025-07-10T10:43:32.013036+00:00] report.DEBUG: Add of item with id Magento_Reports::report_products_lowstock was processed [] []
[2025-07-10T10:43:32.013069+00:00] report.DEBUG: Add of item with id Magento_Reports::report_customers was processed [] []
[2025-07-10T10:43:32.013102+00:00] report.DEBUG: Add of item with id Magento_Reports::report_customers_accounts was processed [] []
[2025-07-10T10:43:32.013135+00:00] report.DEBUG: Add of item with id Magento_Reports::report_customers_totals was processed [] []
[2025-07-10T10:43:32.013168+00:00] report.DEBUG: Add of item with id Magento_Reports::report_customers_orders was processed [] []
[2025-07-10T10:43:32.013201+00:00] report.DEBUG: Add of item with id Magento_Reports::report_statistics was processed [] []
[2025-07-10T10:43:32.013240+00:00] report.DEBUG: Add of item with id Magento_Reports::report_statistics_refresh was processed [] []
[2025-07-10T10:43:32.013274+00:00] report.DEBUG: Add of item with id Magento_Search::report_search_term was processed [] []
[2025-07-10T10:43:32.013307+00:00] report.DEBUG: Add of item with id Magento_Invitation::customer_magento_invitation was processed [] []
[2025-07-10T10:43:32.013342+00:00] report.DEBUG: Add of item with id Magento_Invitation::report_magento_invitation was processed [] []
[2025-07-10T10:43:32.013379+00:00] report.DEBUG: Add of item with id Magento_Invitation::report_magento_invitation_general was processed [] []
[2025-07-10T10:43:32.013413+00:00] report.DEBUG: Add of item with id Magento_Invitation::report_magento_invitation_customer was processed [] []
[2025-07-10T10:43:32.013462+00:00] report.DEBUG: Add of item with id Magento_Invitation::report_magento_invitation_order was processed [] []
[2025-07-10T10:43:32.013496+00:00] report.DEBUG: Add of item with id Magento_Logging::system_magento_logging was processed [] []
[2025-07-10T10:43:32.013533+00:00] report.DEBUG: Add of item with id Magento_Logging::system_magento_logging_events was processed [] []
[2025-07-10T10:43:32.013566+00:00] report.DEBUG: Add of item with id Magento_Logging::system_magento_logging_backups was processed [] []
[2025-07-10T10:43:32.013599+00:00] report.DEBUG: Add of item with id Magento_Logging::system_magento_logging_bulk_operations was processed [] []
[2025-07-10T10:43:32.013633+00:00] report.DEBUG: Add of item with id Magento_MediaGalleryUi::media was processed [] []
[2025-07-10T10:43:32.013670+00:00] report.DEBUG: Add of item with id Magento_MediaGalleryUi::media_gallery was processed [] []
[2025-07-10T10:43:32.013704+00:00] report.DEBUG: Add of item with id Magento_MultipleWishlist::report_customers_wishlist was processed [] []
[2025-07-10T10:43:32.013737+00:00] report.DEBUG: Add of item with id Magento_PageBuilder::templates was processed [] []
[2025-07-10T10:43:32.013770+00:00] report.DEBUG: Add of item with id Magento_PaymentServicesDashboard::paymentservicesdashboard was processed [] []
[2025-07-10T10:43:32.013803+00:00] report.DEBUG: Add of item with id Magento_Reminder::promo_reminder was processed [] []
[2025-07-10T10:43:32.013836+00:00] report.DEBUG: Add of item with id Magento_Review::catalog_reviews_ratings_ratings was processed [] []
[2025-07-10T10:43:32.013873+00:00] report.DEBUG: Add of item with id Magento_Review::catalog_reviews_ratings_pending was processed [] []
[2025-07-10T10:43:32.013906+00:00] report.DEBUG: Add of item with id Magento_Review::catalog_reviews_ratings_reviews_all was processed [] []
[2025-07-10T10:43:32.013939+00:00] report.DEBUG: Add of item with id Magento_Review::report_review was processed [] []
[2025-07-10T10:43:32.013976+00:00] report.DEBUG: Add of item with id Magento_Review::report_review_customer was processed [] []
[2025-07-10T10:43:32.014010+00:00] report.DEBUG: Add of item with id Magento_Review::report_review_product was processed [] []
[2025-07-10T10:43:32.014043+00:00] report.DEBUG: Add of item with id Magento_Reward::customer_reward was processed [] []
[2025-07-10T10:43:32.014076+00:00] report.DEBUG: Add of item with id Magento_Rma::sales_magento_rma_rma was processed [] []
[2025-07-10T10:43:32.014109+00:00] report.DEBUG: Add of item with id Magento_Rma::sales_magento_rma_rma_item_attribute was processed [] []
[2025-07-10T10:43:32.014142+00:00] report.DEBUG: Add of item with id Magento_ScheduledImportExport::system_convert_magento_scheduled_operation was processed [] []
[2025-07-10T10:43:32.014175+00:00] report.DEBUG: Add of item with id Magento_SalesArchive::sales_archive was processed [] []
[2025-07-10T10:43:32.014212+00:00] report.DEBUG: Add of item with id Magento_SalesArchive::sales_archive_orders was processed [] []
[2025-07-10T10:43:32.014246+00:00] report.DEBUG: Add of item with id Magento_SalesArchive::sales_archive_invoices was processed [] []
[2025-07-10T10:43:32.014279+00:00] report.DEBUG: Add of item with id Magento_SalesArchive::sales_archive_shipments was processed [] []
[2025-07-10T10:43:32.014311+00:00] report.DEBUG: Add of item with id Magento_SalesArchive::sales_archive_creditmemos was processed [] []
[2025-07-10T10:43:32.014344+00:00] report.DEBUG: Add of item with id Magento_Company::company_index was processed [] []
[2025-07-10T10:43:32.014379+00:00] report.DEBUG: Add of item with id Magento_Support::support was processed [] []
[2025-07-10T10:43:32.014416+00:00] report.DEBUG: Add of item with id Magento_Support::support_backup was processed [] []
[2025-07-10T10:43:32.014458+00:00] report.DEBUG: Add of item with id Magento_Support::support_report was processed [] []
[2025-07-10T10:43:32.014491+00:00] report.DEBUG: Add of item with id Magento_Swat::system_insights was processed [] []
[2025-07-10T10:43:32.014528+00:00] report.DEBUG: Add of item with id Magento_Swat::swat was processed [] []
[2025-07-10T10:43:32.014562+00:00] report.DEBUG: Add of item with id Magento_TaxImportExport::system_convert_tax was processed [] []
[2025-07-10T10:43:32.014599+00:00] report.DEBUG: Add of item with id Webkul_MpSellerGroup::group was processed [] []
[2025-07-10T10:43:32.014632+00:00] report.DEBUG: Add of item with id Webkul_MpSellerGroup::booster was processed [] []
[2025-07-10T10:43:32.014669+00:00] report.DEBUG: Add of item with id Webkul_MpSellerGroup::manager was processed [] []
[2025-07-10T10:43:32.014702+00:00] report.DEBUG: Add of item with id Comave_Catalog::comave_catalog_blacklist was processed [] []
[2025-07-10T10:43:32.014739+00:00] report.DEBUG: Add of item with id Comave_Catalog::comave_catalog_product_blacklist was processed [] []
[2025-07-10T10:43:32.014773+00:00] report.DEBUG: Add of item with id WeltPixel_Backend::WeltPixel was processed [] []
[2025-07-10T10:43:32.014810+00:00] report.DEBUG: Add of item with id WeltPixel_Backend::WeltPixel_Documentation was processed [] []
[2025-07-10T10:43:32.014843+00:00] report.DEBUG: Add of item with id WeltPixel_Backend::WeltPixel_HelpCenter was processed [] []
[2025-07-10T10:43:32.014876+00:00] report.DEBUG: Add of item with id WeltPixel_Backend::WeltPixel_Licenses was processed [] []
[2025-07-10T10:43:32.014913+00:00] report.DEBUG: Add of item with id WeltPixel_Backend::Modules_License was processed [] []
[2025-07-10T10:43:32.014946+00:00] report.DEBUG: Add of item with id WeltPixel_Backend::WeltPixel_Debugger was processed [] []
[2025-07-10T10:43:32.014983+00:00] report.DEBUG: Add of item with id WeltPixel_Backend::WeltPixel_Debugger_Rewrites was processed [] []
[2025-07-10T10:43:32.015017+00:00] report.DEBUG: Add of item with id WeltPixel_Backend::WeltPixel_Debugger_Modules was processed [] []
[2025-07-10T10:43:32.015050+00:00] report.DEBUG: Add of item with id WeltPixel_Backend::WeltPixel_Debugger_Developer was processed [] []
[2025-07-10T10:43:32.015083+00:00] report.DEBUG: Add of item with id WeltPixel_Backend::Pearl_Theme_Options was processed [] []
[2025-07-10T10:43:32.015116+00:00] report.DEBUG: Add of item with id Comave_CategoryCommission::comave_commission_import_export was processed [] []
[2025-07-10T10:43:32.015153+00:00] report.DEBUG: Add of item with id Comave_CategoryCommission::import_export was processed [] []
[2025-07-10T10:43:32.015186+00:00] report.DEBUG: Add of item with id Webkul_Marketplace::marketplace was processed [] []
[2025-07-10T10:43:32.015223+00:00] report.DEBUG: Add of item with id Webkul_Marketplace::product was processed [] []
[2025-07-10T10:43:32.015257+00:00] report.DEBUG: Add of item with id Webkul_Marketplace::seller was processed [] []
[2025-07-10T10:43:32.015290+00:00] report.DEBUG: Add of item with id Webkul_Marketplace::commission was processed [] []
[2025-07-10T10:43:32.015323+00:00] report.DEBUG: Add of item with id Webkul_Marketplace::transaction was processed [] []
[2025-07-10T10:43:32.015356+00:00] report.DEBUG: Add of item with id Webkul_Marketplace::feedback was processed [] []
[2025-07-10T10:43:32.015389+00:00] report.DEBUG: Add of item with id Webkul_Marketplace::menu was processed [] []
[2025-07-10T10:43:32.015429+00:00] report.DEBUG: Add of item with id Webkul_Marketplace::sellerflag was processed [] []
[2025-07-10T10:43:32.015464+00:00] report.DEBUG: Add of item with id Webkul_Marketplace::productflag was processed [] []
[2025-07-10T10:43:32.015498+00:00] report.DEBUG: Add of item with id Webkul_Marketplace::configattributes was processed [] []
[2025-07-10T10:43:32.015547+00:00] report.DEBUG: Add of item with id Webkul_Marketplace::config_marketplace was processed [] []
[2025-07-10T10:43:32.015584+00:00] report.DEBUG: Add of item with id Webkul_Marketplace::marketplace_support was processed [] []
[2025-07-10T10:43:32.015624+00:00] report.DEBUG: Add of item with id Webkul_Marketplace::marketplace_userguide was processed [] []
[2025-07-10T10:43:32.015658+00:00] report.DEBUG: Add of item with id Webkul_Marketplace::marketplace_extension was processed [] []
[2025-07-10T10:43:32.015691+00:00] report.DEBUG: Add of item with id Webkul_Marketplace::marketplace_uvdesk was processed [] []
[2025-07-10T10:43:32.015724+00:00] report.DEBUG: Add of item with id Webkul_Marketplace::marketplace_services was processed [] []
[2025-07-10T10:43:32.015757+00:00] report.DEBUG: Add of item with id Webkul_Marketplace::marketplace_reviews was processed [] []
[2025-07-10T10:43:32.015790+00:00] report.DEBUG: Add of item with id Comave::seller_payouts was processed [] []
[2025-07-10T10:43:32.015827+00:00] report.DEBUG: Add of item with id Comave_SellerPayouts::transcation_payout was processed [] []
[2025-07-10T10:43:32.015860+00:00] report.DEBUG: Add of item with id WeltPixel_EnhancedEmail::EnhancedEmail_Label was processed [] []
[2025-07-10T10:43:32.015897+00:00] report.DEBUG: Add of item with id WeltPixel_EnhancedEmail::EnhancedEmail_General was processed [] []
[2025-07-10T10:43:32.015931+00:00] report.DEBUG: Add of item with id Webkul_MpMultiShopifyStoreMageConnect::manager was processed [] []
[2025-07-10T10:43:32.015968+00:00] report.DEBUG: Add of item with id Webkul_MpMultiShopifyStoreMageConnect::shopify_account_connect was processed [] []
[2025-07-10T10:43:32.016001+00:00] report.DEBUG: Add of item with id Webkul_MpMultiShopifyStoreMageConnect::listing_templates was processed [] []
[2025-07-10T10:43:32.016035+00:00] report.DEBUG: Add of item with id Webkul_MpMultiShopifyStoreMageConnect::products_pricerules was processed [] []
[2025-07-10T10:43:32.016068+00:00] report.DEBUG: Add of item with id Webkul_MpSellerBuyerCommunication::sellerbuyercommunication_menu was processed [] []
[2025-07-10T10:43:32.016105+00:00] report.DEBUG: Add of item with id Webkul_MpSellerBuyerCommunication::query was processed [] []
[2025-07-10T10:43:32.016143+00:00] report.DEBUG: Add of item with id Comave_Integration::comave_integration was processed [] []
[2025-07-10T10:43:32.016180+00:00] report.DEBUG: Add of item with id Comave_Integration::comave_integration_traceability was processed [] []
[2025-07-10T10:43:32.016213+00:00] report.DEBUG: Add of item with id Comave_Integration::comave_integration_traceability_config was processed [] []
[2025-07-10T10:43:32.016247+00:00] report.DEBUG: Add of item with id WeltPixel_ProductLabels::ProductLabels_Label was processed [] []
[2025-07-10T10:43:32.016283+00:00] report.DEBUG: Add of item with id WeltPixel_ProductLabels::ProductLabelsSettings was processed [] []
[2025-07-10T10:43:32.016317+00:00] report.DEBUG: Add of item with id WeltPixel_ProductLabels::ProductLabels was processed [] []
[2025-07-10T10:43:32.016350+00:00] report.DEBUG: Add of item with id Webkul_MpAdvancedCommission::manager was processed [] []
[2025-07-10T10:43:32.016387+00:00] report.DEBUG: Add of item with id Webkul_MpAdvancedCommission::commissionrules was processed [] []
[2025-07-10T10:43:32.016429+00:00] report.DEBUG: Add of item with id Comave_Club::main_menu was processed [] []
[2025-07-10T10:43:32.016468+00:00] report.DEBUG: Add of item with id Comave_Club::club was processed [] []
[2025-07-10T10:43:32.016506+00:00] report.DEBUG: Add of item with id Comave_Club::club_new was processed [] []
[2025-07-10T10:43:32.016539+00:00] report.DEBUG: Add of item with id Comave_Club::club_manage was processed [] []
[2025-07-10T10:43:32.016572+00:00] report.DEBUG: Add of item with id Comave_Club::club_group_manage was processed [] []
[2025-07-10T10:43:32.016605+00:00] report.DEBUG: Add of item with id Comave_Club::import was processed [] []
[2025-07-10T10:43:32.016638+00:00] report.DEBUG: Add of item with id Comave_Club::configuration was processed [] []
[2025-07-10T10:43:32.016674+00:00] report.DEBUG: Add of item with id Comave_SellerOnboarding::menu was processed [] []
[2025-07-10T10:43:32.016711+00:00] report.DEBUG: Add of item with id Comave_SellerOnboarding::category_source_list was processed [] []
[2025-07-10T10:43:32.016744+00:00] report.DEBUG: Add of item with id Comave_SellerOnboarding::category_mapping_list was processed [] []
[2025-07-10T10:43:32.016777+00:00] report.DEBUG: Add of item with id Comave_SellerOnboarding::category_mapping_list_manual was processed [] []
[2025-07-10T10:43:32.016810+00:00] report.DEBUG: Add of item with id Comave_MediaCleaner::comave_media_cleaner was processed [] []
[2025-07-10T10:43:32.016847+00:00] report.DEBUG: Add of item with id Comave_MediaCleaner::unused_media_list was processed [] []
[2025-07-10T10:43:32.016880+00:00] report.DEBUG: Add of item with id Comave_MediaCleaner::comave_media_cleaner_config was processed [] []
[2025-07-10T10:43:32.016914+00:00] report.DEBUG: Add of item with id Webkul_OutOfStockNotification::notification was processed [] []
[2025-07-10T10:43:32.016951+00:00] report.DEBUG: Add of item with id Webkul_OutOfStockNotification::Request was processed [] []
[2025-07-10T10:43:32.016984+00:00] report.DEBUG: Add of item with id Webkul_OutOfStockNotification::system_config was processed [] []
[2025-07-10T10:43:32.017017+00:00] report.DEBUG: Add of item with id Webkul_OutOfStockNotification::outofstocknotification_support was processed [] []
[2025-07-10T10:43:32.017054+00:00] report.DEBUG: Add of item with id Webkul_OutOfStockNotification::outofstocknotification_userguide was processed [] []
[2025-07-10T10:43:32.017088+00:00] report.DEBUG: Add of item with id Webkul_OutOfStockNotification::outofstocknotification_extension was processed [] []
[2025-07-10T10:43:32.017121+00:00] report.DEBUG: Add of item with id Webkul_OutOfStockNotification::outofstocknotification_uvdesk was processed [] []
[2025-07-10T10:43:32.017155+00:00] report.DEBUG: Add of item with id Webkul_OutOfStockNotification::outofstocknotification_services was processed [] []
[2025-07-10T10:43:32.017188+00:00] report.DEBUG: Add of item with id Webkul_OutOfStockNotification::outofstocknotification_reviews was processed [] []
[2025-07-10T10:43:32.017221+00:00] report.DEBUG: Add of item with id Comave_ReasonManagement::reason_management was processed [] []
[2025-07-10T10:43:32.017254+00:00] report.DEBUG: Add of item with id Comave_ShopifyAccounts::shopify_error_logs was processed [] []
[2025-07-10T10:43:32.017287+00:00] report.DEBUG: Add of item with id Webkul_MpSellerCategory::menu was processed [] []
[2025-07-10T10:43:32.017324+00:00] report.DEBUG: Add of item with id Webkul_MpSellerCategory::category was processed [] []
[2025-07-10T10:43:32.017357+00:00] report.DEBUG: Add of item with id Comave_SellerReport::seller_report was processed [] []
[2025-07-10T10:43:32.017395+00:00] report.DEBUG: Add of item with id Comave_SellerReport::sellers_by_country was processed [] []
[2025-07-10T10:43:32.017440+00:00] report.DEBUG: Add of item with id Coditron_CustomShippingRate::tablerates was processed [] []
[2025-07-10T10:43:32.017504+00:00] report.DEBUG: Add of item with id Coditron_CustomShippingRate::coditron_customshippingrate_shiptablerates was processed [] []
[2025-07-10T10:43:32.017539+00:00] report.DEBUG: Add of item with id StripeIntegration_Tax::stripe_tax_classes was processed [] []
[2025-07-10T10:43:32.017572+00:00] report.DEBUG: Add of item with id Comave::top_level was processed [] []
[2025-07-10T10:43:32.017605+00:00] report.DEBUG: Add of item with id Comave_TravellerInfo::comave_travellerinfo was processed [] []
[2025-07-10T10:43:32.017638+00:00] report.DEBUG: Add of item with id EthanYehuda_CronjobManager::cronjobmanager was processed [] []
[2025-07-10T10:43:32.017672+00:00] report.DEBUG: Add of item with id Lof_All::lof_all was processed [] []
[2025-07-10T10:43:32.017709+00:00] report.DEBUG: Add of item with id Lof_All::license was processed [] []
[2025-07-10T10:43:32.017746+00:00] report.DEBUG: Add of item with id Lof_All::license_manage was processed [] []
[2025-07-10T10:43:32.017782+00:00] report.DEBUG: Add of item with id Lof_All::market_manage was processed [] []
[2025-07-10T10:43:32.017815+00:00] report.DEBUG: Add of item with id Lof_ProductReviews::lof_product_reviews was processed [] []
[2025-07-10T10:43:32.017852+00:00] report.DEBUG: Add of item with id Lof_ProductReviews::all_reviews was processed [] []
[2025-07-10T10:43:32.017885+00:00] report.DEBUG: Add of item with id Lof_ProductReviews::importreviews was processed [] []
[2025-07-10T10:43:32.017918+00:00] report.DEBUG: Add of item with id Lof_ProductReviews::lof_product_reviews_reminders was processed [] []
[2025-07-10T10:43:32.017951+00:00] report.DEBUG: Add of item with id Lof_ProductReviews::lof_product_reviews_config was processed [] []
[2025-07-10T10:43:32.017984+00:00] report.DEBUG: Add of item with id Navigate_Core::core was processed [] []
[2025-07-10T10:43:32.018021+00:00] report.DEBUG: Add of item with id Navigate_AllowSvgWebpAvifImage::main was processed [] []
[2025-07-10T10:43:32.018059+00:00] report.DEBUG: Add of item with id Navigate_AllowSvgWebpAvifImage::configuration was processed [] []
[2025-07-10T10:43:32.018092+00:00] report.DEBUG: Add of item with id PayPal_Braintree::settlement_report was processed [] []
[2025-07-10T10:43:32.018125+00:00] report.DEBUG: Add of item with id PayPal_Braintree::virtual_terminal was processed [] []
[2025-07-10T10:43:32.018158+00:00] report.DEBUG: Add of item with id Webkul_FirebaseOTPLogin::firebaselogin was processed [] []
[2025-07-10T10:43:32.018195+00:00] report.DEBUG: Add of item with id Webkul_FirebaseOTPLogin::config_firebase_otp was processed [] []
[2025-07-10T10:43:32.018232+00:00] report.DEBUG: Add of item with id Webkul_MarketplacePreorder::index was processed [] []
[2025-07-10T10:43:32.018265+00:00] report.DEBUG: Add of item with id Webkul_MarketplacePreorder::menu was processed [] []
[2025-07-10T10:43:32.018299+00:00] report.DEBUG: Add of item with id Comave_ShopifyWebhookInstaller::shopify_webhook_heartbeat was processed [] []
[2025-07-10T10:43:32.018332+00:00] report.DEBUG: Add of item with id Webkul_MpSellerCoupons::menu was processed [] []
[2025-07-10T10:43:32.018369+00:00] report.DEBUG: Add of item with id Webkul_MpSellerCoupons::index was processed [] []
[2025-07-10T10:43:32.018406+00:00] report.DEBUG: Add of item with id Webkul_MpVendorAttributeManager::index was processed [] []
[2025-07-10T10:43:32.018454+00:00] report.DEBUG: Add of item with id Webkul_MpVendorAttributeManager::group was processed [] []
[2025-07-10T10:43:32.018488+00:00] report.DEBUG: Add of item with id Webkul_MpVendorAttributeManager::menu was processed [] []
[2025-07-10T10:43:32.018521+00:00] report.DEBUG: Add of item with id Webkul_Mppercountryperproductshipping::index was processed [] []
[2025-07-10T10:43:32.018554+00:00] report.DEBUG: Add of item with id Webkul_PriceDropAlert::priceDropAlert was processed [] []
[2025-07-10T10:43:32.018591+00:00] report.DEBUG: Add of item with id Webkul_PriceDropAlert::priceDropAlertLog was processed [] []
[2025-07-10T10:43:32.018624+00:00] report.DEBUG: Add of item with id Webkul_PriceDropAlert::pricedropalert_support was processed [] []
[2025-07-10T10:43:32.018661+00:00] report.DEBUG: Add of item with id Webkul_PriceDropAlert::pricedropalert_userguide was processed [] []
[2025-07-10T10:43:32.018694+00:00] report.DEBUG: Add of item with id Webkul_PriceDropAlert::pricedropalert_extension was processed [] []
[2025-07-10T10:43:32.018727+00:00] report.DEBUG: Add of item with id Webkul_PriceDropAlert::pricedropalert_uvdesk was processed [] []
[2025-07-10T10:43:32.018760+00:00] report.DEBUG: Add of item with id Webkul_PriceDropAlert::pricedropalert_services was processed [] []
[2025-07-10T10:43:32.018793+00:00] report.DEBUG: Add of item with id Webkul_PriceDropAlert::pricedropalert_reviews was processed [] []
[2025-07-10T10:43:32.018826+00:00] report.DEBUG: Add of item with id WeltPixel_AdvanceCategorySorting::AdvanceCategorySorting_Label was processed [] []
[2025-07-10T10:43:32.018865+00:00] report.DEBUG: Add of item with id WeltPixel_AdvanceCategorySorting::AdvanceCategorySorting was processed [] []
[2025-07-10T10:43:32.018899+00:00] report.DEBUG: Add of item with id WeltPixel_AdvancedWishlist::AdvancedWishlist_Label was processed [] []
[2025-07-10T10:43:32.018936+00:00] report.DEBUG: Add of item with id WeltPixel_AdvancedWishlist::AdvancedWishlist was processed [] []
[2025-07-10T10:43:32.018969+00:00] report.DEBUG: Add of item with id WeltPixel_CmsBlockScheduler::cmsblockscheduler was processed [] []
[2025-07-10T10:43:32.019006+00:00] report.DEBUG: Add of item with id WeltPixel_CmsBlockScheduler::systemconfiguration was processed [] []
[2025-07-10T10:43:32.019039+00:00] report.DEBUG: Add of item with id WeltPixel_CmsBlockScheduler::cmsblockscheduler_manage_tags was processed [] []
[2025-07-10T10:43:32.019076+00:00] report.DEBUG: Add of item with id WeltPixel_CustomFooter::CustomFooter was processed [] []
[2025-07-10T10:43:32.019110+00:00] report.DEBUG: Add of item with id WeltPixel_CustomHeader::CustomHeader was processed [] []
[2025-07-10T10:43:32.019143+00:00] report.DEBUG: Add of item with id WeltPixel_DesignElements::DesignElements was processed [] []
[2025-07-10T10:43:32.019176+00:00] report.DEBUG: Add of item with id WeltPixel_FullPageScroll::FullPageScroll_Label was processed [] []
[2025-07-10T10:43:32.019213+00:00] report.DEBUG: Add of item with id WeltPixel_FullPageScroll::FullPageScroll was processed [] []
[2025-07-10T10:43:32.019246+00:00] report.DEBUG: Add of item with id WeltPixel_GoogleCards::GoogleCards_Label was processed [] []
[2025-07-10T10:43:32.019283+00:00] report.DEBUG: Add of item with id WeltPixel_GoogleCards::GoogleCards was processed [] []
[2025-07-10T10:43:32.019316+00:00] report.DEBUG: Add of item with id WeltPixel_LazyLoading::LazyLoading_Label was processed [] []
[2025-07-10T10:43:32.019353+00:00] report.DEBUG: Add of item with id WeltPixel_LazyLoading::LazyLoading was processed [] []
[2025-07-10T10:43:32.019386+00:00] report.DEBUG: Add of item with id WeltPixel_Multistore::Multistore_Label was processed [] []
[2025-07-10T10:43:32.019430+00:00] report.DEBUG: Add of item with id WeltPixel_Multistore::Multistore was processed [] []
[2025-07-10T10:43:32.019465+00:00] report.DEBUG: Add of item with id WeltPixel_QuickCart::QuickCart_Label was processed [] []
[2025-07-10T10:43:32.019502+00:00] report.DEBUG: Add of item with id WeltPixel_QuickCart::QuickCart was processed [] []
[2025-07-10T10:43:32.019536+00:00] report.DEBUG: Add of item with id WeltPixel_Sitemap::Sitemap_Label was processed [] []
[2025-07-10T10:43:32.019573+00:00] report.DEBUG: Add of item with id WeltPixel_Sitemap::Sitemap was processed [] []
[2025-07-10T10:43:32.019607+00:00] report.DEBUG: Add of item with id WeltPixel_SmartProductTabs::SmartProductTabs_Label was processed [] []
[2025-07-10T10:43:32.019644+00:00] report.DEBUG: Add of item with id WeltPixel_SmartProductTabs::SmartProductTabs was processed [] []
[2025-07-10T10:43:32.019679+00:00] report.DEBUG: Add of item with id WeltPixel_SmartProductTabs::SmartProductTabsGrid was processed [] []
[2025-07-10T10:43:32.019714+00:00] report.DEBUG: Add of item with id WeltPixel_ThankYouPage::ThankYouPage_Label was processed [] []
[2025-07-10T10:43:32.019751+00:00] report.DEBUG: Add of item with id WeltPixel_ThankYouPage::ThankYouPage was processed [] []
[2025-07-10T10:43:32.019784+00:00] report.DEBUG: Add of item with id WeltPixel_UserProfile::UserProfile_Label was processed [] []
[2025-07-10T10:43:32.019821+00:00] report.DEBUG: Add of item with id WeltPixel_UserProfile::UserProfile was processed [] []
[2025-07-10T10:43:32.235297+00:00] report.INFO: DataProvider afterGetData called {"result_keys":[683],"result_count":1} []
[2025-07-10T10:43:32.235440+00:00] report.INFO: Processing category data {"category_id":683,"has_horizontal_image":false,"has_vertical_image":false,"horizontal_image_value":"not_set","vertical_image_value":"not_set","has_additional_horizontal":false,"has_additional_vertical":false} []
[2025-07-10T10:43:32.235523+00:00] report.INFO: No image data found {"category_id":683,"attribute_code":"horizontal_image","value_type":"NULL","value":"not_set"} []
[2025-07-10T10:43:32.235570+00:00] report.INFO: No image data found {"category_id":683,"attribute_code":"vertical_image","value_type":"NULL","value":"not_set"} []
[2025-07-10T10:43:40.724978+00:00] report.INFO: Broken reference: the 'notification.messages' tries to reorder itself towards 'user', but their parents are different: 'header.inner.right' and 'header' respectively. [] []