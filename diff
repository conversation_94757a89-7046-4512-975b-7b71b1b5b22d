
diff --git a/app/code/Comave/Catalog/Plugin/Category/DataProvider.php b/app/code/Comave/Catalog/Plugin/Category/DataProvider.php
new file mode 100644
index 000000000..ff1b91c1e
--- /dev/null
+++ b/app/code/Comave/Catalog/Plugin/Category/DataProvider.php
@@ -0,0 +1,186 @@
+<?php
+/**
+ * Copyright © Commercial Avenue
+ */
+
+namespace Comave\Catalog\Plugin\Category;
+
+use Magento\Catalog\Model\Category\DataProvider as CategoryDataProvider;
+use Magento\Framework\App\Filesystem\DirectoryList;
+use Magento\Framework\Filesystem;
+use Magento\Framework\UrlInterface;
+use Magento\Store\Model\StoreManagerInterface;
+
+/**
+ * Plugin to convert custom orientation image attributes for UI components
+ */
+class DataProvider
+{
+    /**
+     * Orientation image attributes that need UI component conversion
+     */
+    public const ORIENTATION_IMAGE_ATTRIBUTES = ['horizontal_image', 'vertical_image'];
+
+    /**
+     * MIME type mappings for common image extensions
+     */
+    public const MIME_TYPES = [
+        'jpg' => 'image/jpeg',
+        'jpeg' => 'image/jpeg',
+        'png' => 'image/png',
+        'gif' => 'image/gif',
+        'webp' => 'image/webp',
+        'svg' => 'image/svg+xml',
+        'avif' => 'image/avif'
+    ];
+
+    /**
+     * @param Filesystem $filesystem
+     * @param StoreManagerInterface $storeManager
+     */
+    public function __construct(
+        private readonly Filesystem $filesystem,
+        private readonly StoreManagerInterface $storeManager
+    ) {
+    }
+
+    /**
+     * Convert orientation image attributes to UI component format
+     *
+     * @param CategoryDataProvider $subject
+     * @param array $result
+     * @return array
+     */
+    public function afterGetData(CategoryDataProvider $subject, array $result): array
+    {
+
+
+        foreach ($result as $categoryId => &$categoryData) {
+            if (!$this->isValidCategoryData($categoryData)) {
+                continue;
+            }
+
+            foreach (self::ORIENTATION_IMAGE_ATTRIBUTES as $attributeCode) {
+                if ($this->hasImageData($categoryData, $attributeCode)) {
+                    $convertedData = $this->convertImageToUiFormat($categoryData[$attributeCode]);
+                    if ($convertedData) {
+                        $categoryData[$attributeCode] = $convertedData;
+
+                    }
+                }
+            }
+        }
+
+        return $result;
+    }
+
+    /**
+     * Check if category data is valid for processing
+     *
+     * @param mixed $categoryData
+     * @return bool
+     */
+    private function isValidCategoryData($categoryData): bool
+    {
+        return is_array($categoryData) && isset($categoryData['entity_id']);
+    }
+
+    /**
+     * Check if category has valid image data for attribute
+     *
+     * @param array $categoryData
+     * @param string $attributeCode
+     * @return bool
+     */
+    private function hasImageData(array $categoryData, string $attributeCode): bool
+    {
+        return isset($categoryData[$attributeCode]) &&
+               is_string($categoryData[$attributeCode]) &&
+               !empty($categoryData[$attributeCode]);
+    }
+
+    /**
+     * Convert image path to UI component format
+     *
+     * @param string $imagePath
+     * @return array|null
+     */
+    private function convertImageToUiFormat(string $imagePath): ?array
+    {
+        try {
+            [$relativePath, $imageUrl] = $this->resolveImagePaths($imagePath);
+
+            if (!$this->isImageFileExists($relativePath)) {
+                return null;
+            }
+
+            $fileInfo = $this->getImageFileInfo($relativePath);
+
+            return [
+                [
+                    'name' => basename($relativePath),
+                    'url' => $imageUrl,
+                    'size' => $fileInfo['size'],
+                    'type' => $fileInfo['mime_type']
+                ]
+            ];
+        } catch (\Exception $e) {
+            return null;
+        }
+    }
+
+    /**
+     * Resolve relative and absolute paths for image
+     *
+     * @param string $imagePath
+     * @return array [relativePath, imageUrl]
+     */
+    private function resolveImagePaths(string $imagePath): array
+    {
+        $baseUrl = $this->storeManager->getStore()->getBaseUrl(UrlInterface::URL_TYPE_MEDIA);
+
+        if (strpos($imagePath, 'http') === 0) {
+            $relativePath = str_replace($baseUrl, '', $imagePath);
+            $imageUrl = $imagePath;
+        } else {
+            $relativePath = $imagePath;
+            $imageUrl = $baseUrl . $relativePath;
+        }
+
+        return [$relativePath, $imageUrl];
+    }
+
+    /**
+     * Check if image file exists
+     *
+     * @param string $relativePath
+     * @return bool
+     */
+    private function isImageFileExists(string $relativePath): bool
+    {
+        $mediaDirectory = $this->filesystem->getDirectoryRead(DirectoryList::MEDIA);
+        $exists = $mediaDirectory->isExist($relativePath);
+
+
+
+        return $exists;
+    }
+
+    /**
+     * Get image file information
+     *
+     * @param string $relativePath
+     * @return array
+     */
+    private function getImageFileInfo(string $relativePath): array
+    {
+        $mediaDirectory = $this->filesystem->getDirectoryRead(DirectoryList::MEDIA);
+        $stat = $mediaDirectory->stat($relativePath);
+        $extension = strtolower(pathinfo($relativePath, PATHINFO_EXTENSION));
+
+        return [
+            'size' => $stat['size'] ?? 0,
+            'mime_type' => self::MIME_TYPES[$extension] ?? 'image/jpeg'
+        ];
+    }
+}
diff --git a/app/code/Comave/Catalog/Setup/Patch/Data/CreateCategoryOrientationImageFields.php b/app/code/Comave/Catalog/Setup/Patch/Data/CreateCategoryOrientationImageFields.php
new file mode 100644
index 000000000..5c739bc3f
--- /dev/null
+++ b/app/code/Comave/Catalog/Setup/Patch/Data/CreateCategoryOrientationImageFields.php
@@ -0,0 +1,116 @@
+<?php
+/**
+ * Copyright © Commercial Avenue
+ */
+
+namespace Comave\Catalog\Setup\Patch\Data;
+
+use Magento\Catalog\Model\Category;
+use Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface;
+use Magento\Eav\Setup\EavSetup;
+use Magento\Eav\Setup\EavSetupFactory;
+use Magento\Framework\Setup\ModuleDataSetupInterface;
+use Magento\Framework\Setup\Patch\DataPatchInterface;
+
+/**
+ * Create category horizontal and vertical image attributes
+ */
+class CreateCategoryOrientationImageFields implements DataPatchInterface
+{
+    /**
+     * Attribute names
+     */
+    public const HORIZONTAL_IMAGE_ATTRIBUTE = 'horizontal_image';
+    public const VERTICAL_IMAGE_ATTRIBUTE = 'vertical_image';
+
+    /**
+     * Attribute labels
+     */
+    public const HORIZONTAL_IMAGE_LABEL = 'Horizontal Image';
+    public const VERTICAL_IMAGE_LABEL = 'Vertical Image';
+
+    /**
+     * Attribute configuration
+     */
+    public const ATTRIBUTE_TYPE = 'varchar';
+    public const ATTRIBUTE_INPUT = 'image';
+    public const ATTRIBUTE_GROUP = 'Content';
+    public const HORIZONTAL_SORT_ORDER = 41;
+    public const VERTICAL_SORT_ORDER = 42;
+    /**
+     * @param ModuleDataSetupInterface $moduleDataSetup
+     * @param EavSetupFactory $eavSetupFactory
+     */
+    public function __construct(
+        private readonly ModuleDataSetupInterface $moduleDataSetup,
+        private readonly EavSetupFactory $eavSetupFactory
+    ) {
+    }
+
+    /**
+     * @inheritdoc
+     */
+    public function apply()
+    {
+        $this->moduleDataSetup->getConnection()->startSetup();
+
+        /** @var EavSetup $eavSetup */
+        $eavSetup = $this->eavSetupFactory->create(['setup' => $this->moduleDataSetup]);
+
+        $eavSetup->addAttribute(
+            Category::ENTITY,
+            self::HORIZONTAL_IMAGE_ATTRIBUTE,
+            [
+                'type' => self::ATTRIBUTE_TYPE,
+                'label' => self::HORIZONTAL_IMAGE_LABEL,
+                'input' => self::ATTRIBUTE_INPUT,
+                'backend' => \Comave\Catalog\Model\Category\Attribute\Backend\OrientationImage::class,
+                'required' => false,
+                'sort_order' => self::HORIZONTAL_SORT_ORDER,
+                'global' => ScopedAttributeInterface::SCOPE_STORE,
+                'group' => self::ATTRIBUTE_GROUP,
+                'used_in_product_listing' => false,
+                'visible_on_front' => false,
+                'user_defined' => false,
+                'default' => '',
+            ]
+        );
+
+        $eavSetup->addAttribute(
+            Category::ENTITY,
+            self::VERTICAL_IMAGE_ATTRIBUTE,
+            [
+                'type' => self::ATTRIBUTE_TYPE,
+                'label' => self::VERTICAL_IMAGE_LABEL,
+                'input' => self::ATTRIBUTE_INPUT,
+                'backend' => \Comave\Catalog\Model\Category\Attribute\Backend\OrientationImage::class,
+                'required' => false,
+                'sort_order' => self::VERTICAL_SORT_ORDER,
+                'global' => ScopedAttributeInterface::SCOPE_STORE,
+                'group' => self::ATTRIBUTE_GROUP,
+                'used_in_product_listing' => false,
+                'visible_on_front' => false,
+                'user_defined' => false,
+                'default' => '',
+            ]
+        );
+
+        $this->moduleDataSetup->getConnection()->endSetup();
+    }
+
+    /**
+     * @inheritdoc
+     */
+    public static function getDependencies()
+    {
+        return [];
+    }
+
+    /**
+     * @inheritdoc
+     */
+    public function getAliases()
+    {
+        return [];
+    }
+}
diff --git a/app/code/Comave/Catalog/etc/di.xml b/app/code/Comave/Catalog/etc/di.xml
index d6bf244cd..d36a36437 100644
--- a/app/code/Comave/Catalog/etc/di.xml
+++ b/app/code/Comave/Catalog/etc/di.xml
@@ -67,4 +67,23 @@
                 type="Comave\Catalog\Plugin\ProductVariantImageInheritancePlugin"
                 sortOrder="10"/>
     </type>
+
+    <type name="Comave\Catalog\Model\Category\Attribute\Backend\OrientationImage">
+        <arguments>
+            <argument name="fileUploaderFactory" xsi:type="object">Magento\MediaStorage\Model\File\UploaderFactory</argument>
+            <argument name="filesystem" xsi:type="object">Magento\Framework\Filesystem</argument>
+            <argument name="storeManager" xsi:type="object">Magento\Store\Model\StoreManagerInterface</argument>
+        </arguments>
+    </type>
+
+    <type name="Comave\Catalog\Plugin\Category\DataProvider">
+        <arguments>
+            <argument name="filesystem" xsi:type="object">Magento\Framework\Filesystem</argument>
+            <argument name="storeManager" xsi:type="object">Magento\Store\Model\StoreManagerInterface</argument>
+        </arguments>
+    </type>
+
+    <type name="Magento\Catalog\Model\Category\DataProvider">
+        <plugin name="comave_catalog_category_data_provider" type="Comave\Catalog\Plugin\Category\DataProvider" sortOrder="10"/>
+    </type>
 </config>
diff --git a/app/code/Comave/Catalog/view/adminhtml/ui_component/category_form.xml b/app/code/Comave/Catalog/view/adminhtml/ui_component/category_form.xml
new file mode 100644
index 000000000..a5a63a3ec
--- /dev/null
+++ b/app/code/Comave/Catalog/view/adminhtml/ui_component/category_form.xml
@@ -0,0 +1,63 @@
+<?xml version="1.0" encoding="UTF-8"?>
+<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
+    <fieldset name="content" sortOrder="10">
+        <field name="horizontal_image" sortOrder="41" formElement="imageUploader">
+            <argument name="data" xsi:type="array">
+                <item name="config" xsi:type="array">
+                    <item name="source" xsi:type="string">category</item>
+                </item>
+            </argument>
+            <settings>
+                <elementTmpl>ui/form/element/uploader/image</elementTmpl>
+                <dataType>string</dataType>
+                <label translate="true">Horizontal Image</label>
+                <visible>true</visible>
+                <required>false</required>
+            </settings>
+            <formElements>
+                <imageUploader>
+                    <settings>
+                        <required>false</required>
+                        <uploaderConfig>
+                            <param xsi:type="url" name="url" path="comave_catalog/category_image/upload"/>
+                        </uploaderConfig>
+                        <previewTmpl>Magento_Catalog/image-preview</previewTmpl>
+                        <openDialogTitle>Media Gallery</openDialogTitle>
+                        <initialMediaGalleryOpenSubpath>catalog/category</initialMediaGalleryOpenSubpath>
+                        <allowedExtensions>jpg jpeg gif png webp avif jfif svg</allowedExtensions>
+                        <maxFileSize>4194304</maxFileSize>
+                    </settings>
+                </imageUploader>
+            </formElements>
+        </field>
+        <field name="vertical_image" sortOrder="42" formElement="imageUploader">
+            <argument name="data" xsi:type="array">
+                <item name="config" xsi:type="array">
+                    <item name="source" xsi:type="string">category</item>
+                </item>
+            </argument>
+            <settings>
+                <elementTmpl>ui/form/element/uploader/image</elementTmpl>
+                <dataType>string</dataType>
+                <label translate="true">Vertical Image</label>
+                <visible>true</visible>
+                <required>false</required>
+            </settings>
+            <formElements>
+                <imageUploader>
+                    <settings>
+                        <required>false</required>
+                        <uploaderConfig>
+                            <param xsi:type="url" name="url" path="comave_catalog/category_image/upload"/>
+                        </uploaderConfig>
+                        <previewTmpl>Magento_Catalog/image-preview</previewTmpl>
+                        <openDialogTitle>Media Gallery</openDialogTitle>
+                        <initialMediaGalleryOpenSubpath>catalog/category</initialMediaGalleryOpenSubpath>
+                        <allowedExtensions>jpg jpeg gif png webp avif jfif svg</allowedExtensions>
+                        <maxFileSize>4194304</maxFileSize>
+                    </settings>
+                </imageUploader>
+            </formElements>
+        </field>
+    </fieldset>
+</form>
diff --git a/app/code/Comave/CatalogGraphQl/Model/Resolver/Category/OrientationImage.php b/app/code/Comave/CatalogGraphQl/Model/Resolver/Category/OrientationImage.php
new file mode 100644
index 000000000..9fa7d6d31
--- /dev/null
+++ b/app/code/Comave/CatalogGraphQl/Model/Resolver/Category/OrientationImage.php
@@ -0,0 +1,121 @@
+<?php
+/**
+ * Copyright © Commercial Avenue
+ */
+
+namespace Comave\CatalogGraphQl\Model\Resolver\Category;
+
+use Magento\Catalog\Model\Category;
+use Magento\Framework\Exception\LocalizedException;
+use Magento\Framework\GraphQl\Config\Element\Field;
+use Magento\Framework\GraphQl\Query\ResolverInterface;
+use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
+use Magento\Framework\UrlInterface;
+use Magento\Store\Model\StoreManagerInterface;
+
+/**
+ * Unified resolver for category orientation images (horizontal/vertical)
+ */
+class OrientationImage implements ResolverInterface
+{
+    /**
+     * URL detection
+     */
+    public const HTTP_PROTOCOL_PREFIX = 'http';
+
+    /**
+     * @param StoreManagerInterface $storeManager
+     */
+    public function __construct(
+        private readonly StoreManagerInterface $storeManager
+    ) {
+    }
+
+    /**
+     * Resolve orientation image URL
+     *
+     * @param Field $field
+     * @param mixed $context
+     * @param ResolveInfo $info
+     * @param array|null $value
+     * @param array|null $args
+     * @return string|null
+     * @throws LocalizedException
+     */
+    public function resolve(Field $field, $context, ResolveInfo $info, array $value = null, array $args = null): ?string
+    {
+        if (!isset($value['model']) || !$value['model'] instanceof Category) {
+            throw new LocalizedException(__('"model" value should be specified'));
+        }
+
+        /** @var Category $category */
+        $category = $value['model'];
+        $attributeName = $field->getName();
+        $imageValue = $category->getData($attributeName);
+
+        if (!$imageValue) {
+            return null;
+        }
+
+        $imagePath = $this->extractImagePath($imageValue);
+
+        if (!$imagePath) {
+            return null;
+        }
+        return $this->buildImageUrl($imagePath);
+    }
+
+    /**
+     * Build full image URL from relative path
+     *
+     * @param string $imagePath
+     * @return string|null
+     */
+    private function buildImageUrl(string $imagePath): ?string
+    {
+        try {
+            if (strpos($imagePath, self::HTTP_PROTOCOL_PREFIX) === 0) {
+                return $imagePath;
+            }
+
+            $mediaUrl = $this->storeManager->getStore()->getBaseUrl(UrlInterface::URL_TYPE_MEDIA);
+            return $mediaUrl . ltrim($imagePath, '/');
+        } catch (\Exception $e) {
+            return null;
+        }
+    }
+
+    /**
+     * Extract image path from value (handles both string and array formats)
+     *
+     * @param mixed $imageValue
+     * @return string|null
+     */
+    private function extractImagePath($imageValue): ?string
+    {
+        if (is_string($imageValue)) {
+            return $imageValue;
+        }
+
+        if (is_array($imageValue) && isset($imageValue[0])) {
+            $firstItem = $imageValue[0];
+
+            if (isset($firstItem['file'])) {
+                return $firstItem['file'];
+            }
+
+            if (isset($firstItem['url'])) {
+                $url = $firstItem['url'];
+                $mediaUrl = $this->storeManager->getStore()->getBaseUrl(\Magento\Framework\UrlInterface::URL_TYPE_MEDIA);
+                $relativePath = str_replace($mediaUrl, '', $url);
+                return ltrim($relativePath, '/');
+            }
+
+            if (isset($firstItem['name'])) {
+                return $firstItem['name'];
+            }
+        }
+
+        return null;
+    }
+}
diff --git a/app/code/Comave/CatalogGraphQl/etc/di.xml b/app/code/Comave/CatalogGraphQl/etc/di.xml
index b7e565f4f..ed48499a7 100644
--- a/app/code/Comave/CatalogGraphQl/etc/di.xml
+++ b/app/code/Comave/CatalogGraphQl/etc/di.xml
@@ -9,4 +9,10 @@
                 type="Comave\CatalogGraphQl\Plugin\RemoveDisabledProducts"
                 sortOrder="1"/>
     </type>
+
+    <type name="Comave\CatalogGraphQl\Model\Resolver\Category\OrientationImage">
+        <arguments>
+            <argument name="storeManager" xsi:type="object">Magento\Store\Model\StoreManagerInterface</argument>
+        </arguments>
+    </type>
 </config>
diff --git a/app/code/Comave/CatalogGraphQl/etc/schema.graphqls b/app/code/Comave/CatalogGraphQl/etc/schema.graphqls
index 37d1df137..46b15047d 100644
--- a/app/code/Comave/CatalogGraphQl/etc/schema.graphqls
+++ b/app/code/Comave/CatalogGraphQl/etc/schema.graphqls
@@ -58,4 +58,26 @@ type VisibleFrontendAttribute {
 input ProductAttributeFilterInput {
     brand: FilterEqualTypeInput @doc(description: "Product Data filter with Brand Value")
     status: FilterEqualTypeInput @doc(description: "Filter products by status (1 for Enabled, 2 for Disabled).")
-}
\ No newline at end of file
+}
+
+interface CategoryInterface {
+    horizontal_image: String
+        @doc(description: "Category horizontal image URL")
+        @resolver(
+            class: "\\Comave\\CatalogGraphQl\\Model\\Resolver\\Category\\OrientationImage"
+        )
+    vertical_image: String
+        @doc(description: "Category vertical image URL")
+        @resolver(
+            class: "\\Comave\\CatalogGraphQl\\Model\\Resolver\\Category\\OrientationImage"
+        )
+}
+
+input ProductAttributeFilterInput {
+    brand: FilterEqualTypeInput
+        @doc(description: "Product Data filter with Brand Value")
+    status: FilterEqualTypeInput
+        @doc(
+            description: "Filter products by status (1 for Enabled, 2 for Disabled)."
+        )
+}
