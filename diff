

[2025-07-10T10:43:32.016247+00:00] report.DEBUG: Add of item with id WeltPixel_ProductLabels::ProductLabels_Label was processed [] []
[2025-07-10T10:43:32.016283+00:00] report.DEBUG: Add of item with id WeltPixel_ProductLabels::ProductLabelsSettings was processed [] []
[2025-07-10T10:43:32.016317+00:00] report.DEBUG: Add of item with id WeltPixel_ProductLabels::ProductLabels was processed [] []
[2025-07-10T10:43:32.016350+00:00] report.DEBUG: Add of item with id Webkul_MpAdvancedCommission::manager was processed [] []
[2025-07-10T10:43:32.016387+00:00] report.DEBUG: Add of item with id Webkul_MpAdvancedCommission::commissionrules was processed [] []
[2025-07-10T10:43:32.016429+00:00] report.DEBUG: Add of item with id Comave_Club::main_menu was processed [] []
[2025-07-10T10:43:32.016468+00:00] report.DEBUG: Add of item with id Comave_Club::club was processed [] []
[2025-07-10T10:43:32.016506+00:00] report.DEBUG: Add of item with id Comave_Club::club_new was processed [] []
[2025-07-10T10:43:32.016539+00:00] report.DEBUG: Add of item with id Comave_Club::club_manage was processed [] []
[2025-07-10T10:43:32.016572+00:00] report.DEBUG: Add of item with id Comave_Club::club_group_manage was processed [] []
[2025-07-10T10:43:32.016605+00:00] report.DEBUG: Add of item with id Comave_Club::import was processed [] []
[2025-07-10T10:43:32.016638+00:00] report.DEBUG: Add of item with id Comave_Club::configuration was processed [] []
[2025-07-10T10:43:32.016674+00:00] report.DEBUG: Add of item with id Comave_SellerOnboarding::menu was processed [] []
[2025-07-10T10:43:32.016711+00:00] report.DEBUG: Add of item with id Comave_SellerOnboarding::category_source_list was processed [] []
[2025-07-10T10:43:32.016744+00:00] report.DEBUG: Add of item with id Comave_SellerOnboarding::category_mapping_list was processed [] []
[2025-07-10T10:43:32.016777+00:00] report.DEBUG: Add of item with id Comave_SellerOnboarding::category_mapping_list_manual was processed [] []
[2025-07-10T10:43:32.016810+00:00] report.DEBUG: Add of item with id Comave_MediaCleaner::comave_media_cleaner was processed [] []
[2025-07-10T10:43:32.016847+00:00] report.DEBUG: Add of item with id Comave_MediaCleaner::unused_media_list was processed [] []
[2025-07-10T10:43:32.016880+00:00] report.DEBUG: Add of item with id Comave_MediaCleaner::comave_media_cleaner_config was processed [] []
[2025-07-10T10:43:32.016914+00:00] report.DEBUG: Add of item with id Webkul_OutOfStockNotification::notification was processed [] []
[2025-07-10T10:43:32.016951+00:00] report.DEBUG: Add of item with id Webkul_OutOfStockNotification::Request was processed [] []
[2025-07-10T10:43:32.016984+00:00] report.DEBUG: Add of item with id Webkul_OutOfStockNotification::system_config was processed [] []
[2025-07-10T10:43:32.017017+00:00] report.DEBUG: Add of item with id Webkul_OutOfStockNotification::outofstocknotification_support was processed [] []
[2025-07-10T10:43:32.017054+00:00] report.DEBUG: Add of item with id Webkul_OutOfStockNotification::outofstocknotification_userguide was processed [] []
[2025-07-10T10:43:32.017088+00:00] report.DEBUG: Add of item with id Webkul_OutOfStockNotification::outofstocknotification_extension was processed [] []
[2025-07-10T10:43:32.017121+00:00] report.DEBUG: Add of item with id Webkul_OutOfStockNotification::outofstocknotification_uvdesk was processed [] []
[2025-07-10T10:43:32.017155+00:00] report.DEBUG: Add of item with id Webkul_OutOfStockNotification::outofstocknotification_services was processed [] []
[2025-07-10T10:43:32.017188+00:00] report.DEBUG: Add of item with id Webkul_OutOfStockNotification::outofstocknotification_reviews was processed [] []
[2025-07-10T10:43:32.017221+00:00] report.DEBUG: Add of item with id Comave_ReasonManagement::reason_management was processed [] []
[2025-07-10T10:43:32.017254+00:00] report.DEBUG: Add of item with id Comave_ShopifyAccounts::shopify_error_logs was processed [] []
[2025-07-10T10:43:32.017287+00:00] report.DEBUG: Add of item with id Webkul_MpSellerCategory::menu was processed [] []
[2025-07-10T10:43:32.017324+00:00] report.DEBUG: Add of item with id Webkul_MpSellerCategory::category was processed [] []
[2025-07-10T10:43:32.017357+00:00] report.DEBUG: Add of item with id Comave_SellerReport::seller_report was processed [] []
[2025-07-10T10:43:32.017395+00:00] report.DEBUG: Add of item with id Comave_SellerReport::sellers_by_country was processed [] []
[2025-07-10T10:43:32.017440+00:00] report.DEBUG: Add of item with id Coditron_CustomShippingRate::tablerates was processed [] []
[2025-07-10T10:43:32.017504+00:00] report.DEBUG: Add of item with id Coditron_CustomShippingRate::coditron_customshippingrate_shiptablerates was processed [] []
[2025-07-10T10:43:32.017539+00:00] report.DEBUG: Add of item with id StripeIntegration_Tax::stripe_tax_classes was processed [] []
[2025-07-10T10:43:32.017572+00:00] report.DEBUG: Add of item with id Comave::top_level was processed [] []
[2025-07-10T10:43:32.017605+00:00] report.DEBUG: Add of item with id Comave_TravellerInfo::comave_travellerinfo was processed [] []
[2025-07-10T10:43:32.017638+00:00] report.DEBUG: Add of item with id EthanYehuda_CronjobManager::cronjobmanager was processed [] []
[2025-07-10T10:43:32.017672+00:00] report.DEBUG: Add of item with id Lof_All::lof_all was processed [] []
[2025-07-10T10:43:32.017709+00:00] report.DEBUG: Add of item with id Lof_All::license was processed [] []
[2025-07-10T10:43:32.017746+00:00] report.DEBUG: Add of item with id Lof_All::license_manage was processed [] []
[2025-07-10T10:43:32.017782+00:00] report.DEBUG: Add of item with id Lof_All::market_manage was processed [] []
[2025-07-10T10:43:32.017815+00:00] report.DEBUG: Add of item with id Lof_ProductReviews::lof_product_reviews was processed [] []
[2025-07-10T10:43:32.017852+00:00] report.DEBUG: Add of item with id Lof_ProductReviews::all_reviews was processed [] []
[2025-07-10T10:43:32.017885+00:00] report.DEBUG: Add of item with id Lof_ProductReviews::importreviews was processed [] []
[2025-07-10T10:43:32.017918+00:00] report.DEBUG: Add of item with id Lof_ProductReviews::lof_product_reviews_reminders was processed [] []
[2025-07-10T10:43:32.017951+00:00] report.DEBUG: Add of item with id Lof_ProductReviews::lof_product_reviews_config was processed [] []
[2025-07-10T10:43:32.017984+00:00] report.DEBUG: Add of item with id Navigate_Core::core was processed [] []
[2025-07-10T10:43:32.018021+00:00] report.DEBUG: Add of item with id Navigate_AllowSvgWebpAvifImage::main was processed [] []
[2025-07-10T10:43:32.018059+00:00] report.DEBUG: Add of item with id Navigate_AllowSvgWebpAvifImage::configuration was processed [] []
[2025-07-10T10:43:32.018092+00:00] report.DEBUG: Add of item with id PayPal_Braintree::settlement_report was processed [] []
[2025-07-10T10:43:32.018125+00:00] report.DEBUG: Add of item with id PayPal_Braintree::virtual_terminal was processed [] []
[2025-07-10T10:43:32.018158+00:00] report.DEBUG: Add of item with id Webkul_FirebaseOTPLogin::firebaselogin was processed [] []
[2025-07-10T10:43:32.018195+00:00] report.DEBUG: Add of item with id Webkul_FirebaseOTPLogin::config_firebase_otp was processed [] []
[2025-07-10T10:43:32.018232+00:00] report.DEBUG: Add of item with id Webkul_MarketplacePreorder::index was processed [] []
[2025-07-10T10:43:32.018265+00:00] report.DEBUG: Add of item with id Webkul_MarketplacePreorder::menu was processed [] []
[2025-07-10T10:43:32.018299+00:00] report.DEBUG: Add of item with id Comave_ShopifyWebhookInstaller::shopify_webhook_heartbeat was processed [] []
[2025-07-10T10:43:32.018332+00:00] report.DEBUG: Add of item with id Webkul_MpSellerCoupons::menu was processed [] []
[2025-07-10T10:43:32.018369+00:00] report.DEBUG: Add of item with id Webkul_MpSellerCoupons::index was processed [] []
[2025-07-10T10:43:32.018406+00:00] report.DEBUG: Add of item with id Webkul_MpVendorAttributeManager::index was processed [] []
[2025-07-10T10:43:32.018454+00:00] report.DEBUG: Add of item with id Webkul_MpVendorAttributeManager::group was processed [] []
[2025-07-10T10:43:32.018488+00:00] report.DEBUG: Add of item with id Webkul_MpVendorAttributeManager::menu was processed [] []
[2025-07-10T10:43:32.018521+00:00] report.DEBUG: Add of item with id Webkul_Mppercountryperproductshipping::index was processed [] []
[2025-07-10T10:43:32.018554+00:00] report.DEBUG: Add of item with id Webkul_PriceDropAlert::priceDropAlert was processed [] []
[2025-07-10T10:43:32.018591+00:00] report.DEBUG: Add of item with id Webkul_PriceDropAlert::priceDropAlertLog was processed [] []
[2025-07-10T10:43:32.018624+00:00] report.DEBUG: Add of item with id Webkul_PriceDropAlert::pricedropalert_support was processed [] []
[2025-07-10T10:43:32.018661+00:00] report.DEBUG: Add of item with id Webkul_PriceDropAlert::pricedropalert_userguide was processed [] []
[2025-07-10T10:43:32.018694+00:00] report.DEBUG: Add of item with id Webkul_PriceDropAlert::pricedropalert_extension was processed [] []
[2025-07-10T10:43:32.018727+00:00] report.DEBUG: Add of item with id Webkul_PriceDropAlert::pricedropalert_uvdesk was processed [] []
[2025-07-10T10:43:32.018760+00:00] report.DEBUG: Add of item with id Webkul_PriceDropAlert::pricedropalert_services was processed [] []
[2025-07-10T10:43:32.018793+00:00] report.DEBUG: Add of item with id Webkul_PriceDropAlert::pricedropalert_reviews was processed [] []
[2025-07-10T10:43:32.018826+00:00] report.DEBUG: Add of item with id WeltPixel_AdvanceCategorySorting::AdvanceCategorySorting_Label was processed [] []
[2025-07-10T10:43:32.018865+00:00] report.DEBUG: Add of item with id WeltPixel_AdvanceCategorySorting::AdvanceCategorySorting was processed [] []
[2025-07-10T10:43:32.018899+00:00] report.DEBUG: Add of item with id WeltPixel_AdvancedWishlist::AdvancedWishlist_Label was processed [] []
[2025-07-10T10:43:32.018936+00:00] report.DEBUG: Add of item with id WeltPixel_AdvancedWishlist::AdvancedWishlist was processed [] []
[2025-07-10T10:43:32.018969+00:00] report.DEBUG: Add of item with id WeltPixel_CmsBlockScheduler::cmsblockscheduler was processed [] []
[2025-07-10T10:43:32.019006+00:00] report.DEBUG: Add of item with id WeltPixel_CmsBlockScheduler::systemconfiguration was processed [] []
[2025-07-10T10:43:32.019039+00:00] report.DEBUG: Add of item with id WeltPixel_CmsBlockScheduler::cmsblockscheduler_manage_tags was processed [] []
[2025-07-10T10:43:32.019076+00:00] report.DEBUG: Add of item with id WeltPixel_CustomFooter::CustomFooter was processed [] []
[2025-07-10T10:43:32.019110+00:00] report.DEBUG: Add of item with id WeltPixel_CustomHeader::CustomHeader was processed [] []
[2025-07-10T10:43:32.019143+00:00] report.DEBUG: Add of item with id WeltPixel_DesignElements::DesignElements was processed [] []
[2025-07-10T10:43:32.019176+00:00] report.DEBUG: Add of item with id WeltPixel_FullPageScroll::FullPageScroll_Label was processed [] []
[2025-07-10T10:43:32.019213+00:00] report.DEBUG: Add of item with id WeltPixel_FullPageScroll::FullPageScroll was processed [] []
[2025-07-10T10:43:32.019246+00:00] report.DEBUG: Add of item with id WeltPixel_GoogleCards::GoogleCards_Label was processed [] []
[2025-07-10T10:43:32.019283+00:00] report.DEBUG: Add of item with id WeltPixel_GoogleCards::GoogleCards was processed [] []
[2025-07-10T10:43:32.019316+00:00] report.DEBUG: Add of item with id WeltPixel_LazyLoading::LazyLoading_Label was processed [] []
[2025-07-10T10:43:32.019353+00:00] report.DEBUG: Add of item with id WeltPixel_LazyLoading::LazyLoading was processed [] []
[2025-07-10T10:43:32.019386+00:00] report.DEBUG: Add of item with id WeltPixel_Multistore::Multistore_Label was processed [] []
[2025-07-10T10:43:32.019430+00:00] report.DEBUG: Add of item with id WeltPixel_Multistore::Multistore was processed [] []
[2025-07-10T10:43:32.019465+00:00] report.DEBUG: Add of item with id WeltPixel_QuickCart::QuickCart_Label was processed [] []
[2025-07-10T10:43:32.019502+00:00] report.DEBUG: Add of item with id WeltPixel_QuickCart::QuickCart was processed [] []
[2025-07-10T10:43:32.019536+00:00] report.DEBUG: Add of item with id WeltPixel_Sitemap::Sitemap_Label was processed [] []
[2025-07-10T10:43:32.019573+00:00] report.DEBUG: Add of item with id WeltPixel_Sitemap::Sitemap was processed [] []
[2025-07-10T10:43:32.019607+00:00] report.DEBUG: Add of item with id WeltPixel_SmartProductTabs::SmartProductTabs_Label was processed [] []
[2025-07-10T10:43:32.019644+00:00] report.DEBUG: Add of item with id WeltPixel_SmartProductTabs::SmartProductTabs was processed [] []
[2025-07-10T10:43:32.019679+00:00] report.DEBUG: Add of item with id WeltPixel_SmartProductTabs::SmartProductTabsGrid was processed [] []
[2025-07-10T10:43:32.019714+00:00] report.DEBUG: Add of item with id WeltPixel_ThankYouPage::ThankYouPage_Label was processed [] []
[2025-07-10T10:43:32.019751+00:00] report.DEBUG: Add of item with id WeltPixel_ThankYouPage::ThankYouPage was processed [] []
[2025-07-10T10:43:32.019784+00:00] report.DEBUG: Add of item with id WeltPixel_UserProfile::UserProfile_Label was processed [] []
[2025-07-10T10:43:32.019821+00:00] report.DEBUG: Add of item with id WeltPixel_UserProfile::UserProfile was processed [] []
[2025-07-10T10:43:32.235297+00:00] report.INFO: DataProvider afterGetData called {"result_keys":[683],"result_count":1} []
[2025-07-10T10:43:32.235440+00:00] report.INFO: Processing category data {"category_id":683,"has_horizontal_image":false,"has_vertical_image":false,"horizontal_image_value":"not_set","vertical_image_value":"not_set","has_additional_horizontal":false,"has_additional_vertical":false} []
[2025-07-10T10:43:32.235523+00:00] report.INFO: No image data found {"category_id":683,"attribute_code":"horizontal_image","value_type":"NULL","value":"not_set"} []
[2025-07-10T10:43:32.235570+00:00] report.INFO: No image data found {"category_id":683,"attribute_code":"vertical_image","value_type":"NULL","value":"not_set"} []
[2025-07-10T10:43:40.724978+00:00] report.INFO: Broken reference: the 'notification.messages' tries to reorder itself towards 'user', but their parents are different: 'header.inner.right' and 'header' respectively. [] []