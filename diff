diff --git a/app/code/Comave/Catalog/Controller/Adminhtml/Category/Image/Upload.php b/app/code/Comave/Catalog/Controller/Adminhtml/Category/Image/Upload.php
new file mode 100644
index 000000000..6ace75443
--- /dev/null
+++ b/app/code/Comave/Catalog/Controller/Adminhtml/Category/Image/Upload.php
@@ -0,0 +1,146 @@
+<?php
+/**
+ * Copyright © Commercial Avenue
+ */
+
+namespace Comave\Catalog\Controller\Adminhtml\Category\Image;
+
+use Magento\Backend\App\Action;
+use Magento\Backend\App\Action\Context;
+use Magento\Catalog\Model\ImageUploaderFactory;
+use Magento\Framework\App\Action\HttpPostActionInterface;
+use Magento\Framework\Controller\ResultFactory;
+use Magento\Framework\Controller\ResultInterface;
+use Magento\Framework\Exception\LocalizedException;
+
+/**
+ * Unified controller for category orientation image uploads
+ */
+class Upload extends Action implements HttpPostActionInterface
+{
+    /**
+     * Authorization level of a basic admin session
+     */
+    public const ADMIN_RESOURCE = 'Magento_Catalog::categories';
+
+    /**
+     * Supported orientation types
+     */
+    public const SUPPORTED_ORIENTATIONS = ['horizontal', 'vertical'];
+
+    /**
+     * Default orientation fallback
+     */
+    public const DEFAULT_ORIENTATION = 'horizontal';
+
+    /**
+     * Request parameter names
+     */
+    public const PARAM_NAME = 'param_name';
+    public const DEFAULT_IMAGE_ID = 'image';
+
+    /**
+     * Path templates
+     */
+    public const BASE_TMP_PATH_TEMPLATE = 'catalog/category/%s';
+    public const BASE_PATH_TEMPLATE = 'catalog/category/%s';
+
+    /**
+     * Allowed file extensions
+     */
+    public const ALLOWED_EXTENSIONS = ['gif', 'jpg', 'png', 'jpeg', 'webp', 'avif', 'jfif', 'svg'];
+
+    /**
+     * Error messages
+     */
+    public const ERROR_INVALID_ORIENTATION = 'Invalid orientation type: %1';
+
+    /**
+     * @param Context $context
+     * @param ImageUploaderFactory $imageUploaderFactory
+     */
+    public function __construct(
+        Context $context,
+        private readonly ImageUploaderFactory $imageUploaderFactory
+    ) {
+        parent::__construct($context);
+    }
+
+    /**
+     * Upload file controller action
+     *
+     * @return ResultInterface
+     */
+    public function execute(): ResultInterface
+    {
+        $imageId = $this->_request->getParam(self::PARAM_NAME, self::DEFAULT_IMAGE_ID);
+        $orientation = $this->extractOrientation($imageId);
+
+        try {
+            if (!$this->isValidOrientation($orientation)) {
+                throw new LocalizedException(__(self::ERROR_INVALID_ORIENTATION, $orientation));
+            }
+
+            $imageUploader = $this->createImageUploader($orientation);
+            $result = $imageUploader->saveFileToTmpDir($imageId);
+
+            $result['cookie'] = [
+                'name' => $this->_getSession()->getName(),
+                'value' => $this->_getSession()->getSessionId(),
+                'lifetime' => $this->_getSession()->getCookieLifetime(),
+                'path' => $this->_getSession()->getCookiePath(),
+                'domain' => $this->_getSession()->getCookieDomain(),
+            ];
+        } catch (\Exception $e) {
+            $result = ['error' => $e->getMessage(), 'errorcode' => $e->getCode()];
+        }
+
+        return $this->resultFactory->create(ResultFactory::TYPE_JSON)->setData($result);
+    }
+
+    /**
+     * Extract orientation from image parameter name
+     *
+     * @param string $imageId
+     * @return string
+     */
+    private function extractOrientation(string $imageId): string
+    {
+        foreach (self::SUPPORTED_ORIENTATIONS as $orientation) {
+            if (strpos($imageId, $orientation) !== false) {
+                return $orientation;
+            }
+        }
+        
+        return self::DEFAULT_ORIENTATION;
+    }
+
+    /**
+     * Check if orientation is valid
+     *
+     * @param string $orientation
+     * @return bool
+     */
+    private function isValidOrientation(string $orientation): bool
+    {
+        return in_array($orientation, self::SUPPORTED_ORIENTATIONS, true);
+    }
+
+    /**
+     * Create image uploader for specific orientation
+     *
+     * @param string $orientation
+     * @return \Magento\Catalog\Model\ImageUploader
+     */
+    private function createImageUploader(string $orientation): \Magento\Catalog\Model\ImageUploader
+    {
+        $baseTmpPath = sprintf(self::BASE_TMP_PATH_TEMPLATE, $orientation);
+        $basePath = sprintf(self::BASE_PATH_TEMPLATE, $orientation);
+
+        return $this->imageUploaderFactory->create([
+            'baseTmpPath' => $baseTmpPath,
+            'basePath' => $basePath,
+            'allowedExtensions' => self::ALLOWED_EXTENSIONS
+        ]);
+    }
+}
diff --git a/app/code/Comave/Catalog/Model/Category/Attribute/Backend/OrientationImage.php b/app/code/Comave/Catalog/Model/Category/Attribute/Backend/OrientationImage.php
new file mode 100644
index 000000000..12bb7cfec
--- /dev/null
+++ b/app/code/Comave/Catalog/Model/Category/Attribute/Backend/OrientationImage.php
@@ -0,0 +1,414 @@
+<?php
+/**
+ * Copyright © Commercial Avenue
+ */
+
+namespace Comave\Catalog\Model\Category\Attribute\Backend;
+
+use Magento\Framework\App\Filesystem\DirectoryList;
+use Magento\Framework\Exception\LocalizedException;
+use Magento\Framework\Filesystem;
+use Magento\Framework\UrlInterface;
+use Magento\MediaStorage\Model\File\UploaderFactory;
+use Magento\Store\Model\StoreManagerInterface;
+
+/**
+ * Unified backend model for category orientation images (horizontal/vertical)
+ */
+class OrientationImage extends \Magento\Eav\Model\Entity\Attribute\Backend\AbstractBackend
+{
+    /**
+     * Base upload directory for orientation images
+     */
+    public const BASE_UPLOAD_DIR = 'catalog/category';
+
+    /**
+     * Allowed file extensions
+     */
+    public const ALLOWED_EXTENSIONS = ['gif', 'jpg', 'png', 'jpeg', 'webp', 'avif', 'jfif', 'svg'];
+
+    /**
+     * Additional data prefix for UI components
+     */
+    public const ADDITIONAL_DATA_PREFIX = '_additional_data_';
+
+    /**
+     * @param UploaderFactory $fileUploaderFactory
+     * @param Filesystem $filesystem
+     * @param StoreManagerInterface $storeManager
+     */
+    public function __construct(
+        private readonly UploaderFactory $fileUploaderFactory,
+        private readonly Filesystem $filesystem,
+        private readonly StoreManagerInterface $storeManager
+    ) {
+    }
+
+    /**
+     * Process image upload before saving
+     *
+     * @param \Magento\Framework\DataObject $object
+     * @return $this
+     * @throws LocalizedException
+     */
+    public function beforeSave($object): self
+    {
+        $attributeName = $this->getAttribute()->getName();
+        $value = $object->getData($attributeName);
+
+        if ($this->isUploadData($value)) {
+            $this->processImageUpload($object, $attributeName, $value);
+        } elseif ($this->isGallerySelection($value)) {
+            $this->processGallerySelection($object, $attributeName, $value);
+        } elseif ($this->shouldClearAttribute($value, $object, $attributeName)) {
+            $object->setData($attributeName, null);
+        } elseif (is_string($value) && strpos($value, 'media/') === 0) {
+            $cleanPath = substr($value, 6);
+            $cleanPath = $this->ensureOrientationPath($cleanPath, $attributeName);
+            $object->setData($attributeName, $cleanPath);
+
+        } elseif (is_string($value) && !empty($value) && strpos($value, 'catalog/category/') === 0) {
+            $correctedPath = $this->ensureOrientationPath($value, $attributeName);
+            if ($correctedPath !== $value) {
+                $object->setData($attributeName, $correctedPath);
+            }
+        } else {
+            $originalValue = $object->getOrigData($attributeName);
+            if ($originalValue !== null && ($value === null || $value === '')) {
+                $object->setData($attributeName, $originalValue);
+            }
+        }
+
+        return parent::beforeSave($object);
+    }
+
+    /**
+     * Check if value contains upload data
+     *
+     * @param mixed $value
+     * @return bool
+     */
+    private function isUploadData($value): bool
+    {
+        return is_array($value) && isset($value[0]) &&
+               (isset($value[0]['tmp_name']) || (isset($value[0]['file']) && isset($value[0]['url'])));
+    }
+
+    /**
+     * Check if value is gallery selection (existing image)
+     *
+     * @param mixed $value
+     * @return bool
+     */
+    private function isGallerySelection($value): bool
+    {
+        return is_array($value) && isset($value[0]) &&
+               isset($value[0]['url']) && isset($value[0]['name']) &&
+               !isset($value[0]['tmp_name']) &&
+               strpos($value[0]['url'], '/tmp/') === false;
+    }
+
+    /**
+     * Process gallery selection (existing image)
+     *
+     * @param mixed $object
+     * @param string $attributeName
+     * @param array $value
+     * @return void
+     */
+    private function processGallerySelection($object, string $attributeName, array $value): void
+    {
+        $fileData = $value[0];
+        $imageUrl = $fileData['url'];
+
+        $mediaUrl = $this->storeManager->getStore()->getBaseUrl(\Magento\Framework\UrlInterface::URL_TYPE_MEDIA);
+        $relativePath = str_replace($mediaUrl, '', $imageUrl);
+        $relativePath = ltrim($relativePath, '/');
+
+        if (strpos($relativePath, 'media/') === 0) {
+            $relativePath = substr($relativePath, 6);
+        }
+
+        $finalPath = $this->ensureOrientationPath($relativePath, $attributeName);
+        $object->setData($attributeName, $finalPath);
+    }
+
+    /**
+     * Determine if attribute should be cleared
+     *
+     * @param mixed $value
+     * @param \Magento\Framework\DataObject $object
+     * @param string $attributeName
+     * @return bool
+     */
+    private function shouldClearAttribute($value, $object, string $attributeName): bool
+    {
+        if (is_array($value) && isset($value['delete']) && $value['delete']) {
+            return true;
+        }
+
+        return false;
+    }
+
+    /**
+     * Process image upload
+     *
+     * @param \Magento\Framework\DataObject $object
+     * @param string $attributeName
+     * @param array $value
+     * @throws LocalizedException
+     */
+    private function processImageUpload($object, string $attributeName, array $value): void
+    {
+        $imageName = $this->getUploadedImageName($value);
+        if (!$imageName) {
+            return;
+        }
+
+        try {
+            $fileData = $value[0] ?? $value;
+            
+            if ($this->isUiComponentUpload($fileData)) {
+                $finalPath = $this->processUiComponentUpload($attributeName, $fileData);
+            } elseif ($this->isMediaGalleryUpload($fileData)) {
+                $finalPath = $this->processMediaGalleryUpload($fileData);
+            } else {
+                $finalPath = $this->processDirectUpload($attributeName);
+            }
+
+            if ($finalPath) {
+                $this->setImageData($object, $attributeName, $finalPath, $value);
+            }
+        } catch (\Exception $e) {
+
+            
+            if ($e->getCode() != \Magento\MediaStorage\Model\File\Uploader::TMP_NAME_EMPTY) {
+                throw new LocalizedException(
+                    __('Failed to save %1: %2', $this->getOrientationLabel($attributeName), $e->getMessage())
+                );
+            }
+        }
+    }
+
+    /**
+     * Check if this is a UI component upload (file in tmp directory)
+     *
+     * @param array $fileData
+     * @return bool
+     */
+    private function isUiComponentUpload(array $fileData): bool
+    {
+        return isset($fileData['file']) && isset($fileData['url']) && strpos($fileData['url'], '/tmp/') !== false;
+    }
+
+    /**
+     * Check if this is a media gallery upload (file already in final location)
+     *
+     * @param array $fileData
+     * @return bool
+     */
+    private function isMediaGalleryUpload(array $fileData): bool
+    {
+        return isset($fileData['file']) && isset($fileData['url']) && strpos($fileData['url'], '/tmp/') === false;
+    }
+
+
+
+    /**
+     * Process UI component upload (file already in tmp directory)
+     *
+     * @param string $attributeName
+     * @param array $fileData
+     * @return string|null
+     */
+    private function processUiComponentUpload(string $attributeName, array $fileData): ?string
+    {
+        $mediaDirectory = $this->filesystem->getDirectoryWrite(DirectoryList::MEDIA);
+        $orientation = $this->getOrientation($attributeName);
+        
+        $tmpPath = "tmp/catalog/category/{$orientation}" . $fileData['file'];
+        $finalPath = self::BASE_UPLOAD_DIR . "/{$orientation}" . $fileData['file'];
+        
+        if (!$mediaDirectory->isExist($tmpPath)) {
+            throw new LocalizedException(__('Temporary file not found: %1', $tmpPath));
+        }
+
+        $mediaDirectory->copyFile($tmpPath, $finalPath);
+        $mediaDirectory->delete($tmpPath);
+        
+        return $finalPath;
+    }
+
+    /**
+     * Process media gallery upload (file already in final location)
+     *
+     * @param array $fileData
+     * @return string|null
+     */
+    private function processMediaGalleryUpload(array $fileData): ?string
+    {
+        $baseUrl = $this->storeManager->getStore()->getBaseUrl(\Magento\Framework\UrlInterface::URL_TYPE_MEDIA);
+        $relativePath = str_replace($baseUrl, '', $fileData['url']);
+
+        $mediaDirectory = $this->filesystem->getDirectoryRead(DirectoryList::MEDIA);
+        if (!$mediaDirectory->isExist($relativePath)) {
+            throw new LocalizedException(__('Media gallery file not found: %1', $relativePath));
+        }
+
+
+
+        return $relativePath;
+    }
+
+    /**
+     * Process direct upload
+     *
+     * @param string $attributeName
+     * @return string
+     */
+    private function processDirectUpload(string $attributeName): string
+    {
+        $orientation = $this->getOrientation($attributeName);
+        $uploadDir = self::BASE_UPLOAD_DIR . "/{$orientation}";
+
+        $uploader = $this->fileUploaderFactory->create(['fileId' => $attributeName]);
+        $uploader->setAllowedExtensions(self::ALLOWED_EXTENSIONS);
+        $uploader->setAllowRenameFiles(true);
+        $uploader->setFilesDispersion(true);
+
+        $mediaDirectory = $this->filesystem->getDirectoryWrite(DirectoryList::MEDIA);
+        $result = $uploader->save($mediaDirectory->getAbsolutePath($uploadDir));
+
+        return $uploadDir . '/' . $result['file'];
+    }
+
+    /**
+     * Set image data on object
+     *
+     * @param \Magento\Framework\DataObject $object
+     * @param string $attributeName
+     * @param string $finalPath
+     * @param array $value
+     */
+    private function setImageData($object, string $attributeName, string $finalPath, array $value): void
+    {
+        $baseUrl = $this->storeManager->getStore()->getBaseUrl(UrlInterface::URL_TYPE_MEDIA);
+        $finalUrl = $baseUrl . $finalPath;
+        $value[0]['url'] = $finalUrl;
+        $value[0]['name'] = $finalUrl;
+
+        $object->setData(self::ADDITIONAL_DATA_PREFIX . $attributeName, $value);
+        $object->setData($attributeName, $finalPath);
+
+
+    }
+
+    /**
+     * Get orientation from attribute name
+     *
+     * @param string $attributeName
+     * @return string
+     */
+    private function getOrientationFromAttribute(string $attributeName): string
+    {
+        if (strpos($attributeName, 'horizontal') !== false) {
+            return 'horizontal';
+        } elseif (strpos($attributeName, 'vertical') !== false) {
+            return 'vertical';
+        }
+        return 'horizontal';
+    }
+
+    /**
+     * Ensure path includes orientation folder
+     *
+     * @param string $path
+     * @param string $attributeName
+     * @return string
+     */
+    private function ensureOrientationPath(string $path, string $attributeName): string
+    {
+        $orientation = $this->getOrientationFromAttribute($attributeName);
+        $expectedPrefix = "catalog/category/{$orientation}/";
+
+        if (strpos($path, $expectedPrefix) === 0) {
+            return $path;
+        }
+
+        if (strpos($path, 'catalog/category/') === 0) {
+            $filename = basename($path);
+            $newPath = $expectedPrefix . $filename;
+
+            $this->ensureFileInCorrectLocation($path, $newPath);
+            return $newPath;
+        }
+
+        $filename = basename($path);
+        return $expectedPrefix . $filename;
+    }
+
+    /**
+     * Ensure file exists in the correct orientation folder
+     *
+     * @param string $originalPath
+     * @param string $newPath
+     * @return void
+     */
+    private function ensureFileInCorrectLocation(string $originalPath, string $newPath): void
+    {
+        try {
+            $mediaDirectory = $this->filesystem->getDirectoryWrite(\Magento\Framework\App\Filesystem\DirectoryList::MEDIA);
+            if ($mediaDirectory->isExist($originalPath) && !$mediaDirectory->isExist($newPath)) {
+                $orientationDir = dirname($newPath);
+                $mediaDirectory->create($orientationDir);
+                $mediaDirectory->copyFile($originalPath, $newPath);
+            }
+        } catch (\Exception $e) {
+        }
+    }
+
+    /**
+     * Get uploaded image name from value array
+     *
+     * @param mixed $value
+     * @return string
+     */
+    private function getUploadedImageName($value): string
+    {
+        if (!is_array($value)) {
+            return '';
+        }
+
+        if (isset($value[0]['name'])) {
+            return $value[0]['name'];
+        }
+        
+        if (isset($value['name'])) {
+            return $value['name'];
+        }
+
+        return '';
+    }
+
+    /**
+     * Get orientation from attribute name
+     *
+     * @param string $attributeName
+     * @return string
+     */
+    private function getOrientation(string $attributeName): string
+    {
+        return strpos($attributeName, 'horizontal') !== false ? 'horizontal' : 'vertical';
+    }
+
+    /**
+     * Get orientation label for error messages
+     *
+     * @param string $attributeName
+     * @return string
+     */
+    private function getOrientationLabel(string $attributeName): string
+    {
+        return $this->getOrientation($attributeName) . ' image';
+    }
+}
diff --git a/app/code/Comave/Catalog/Plugin/Category/DataProvider.php b/app/code/Comave/Catalog/Plugin/Category/DataProvider.php
new file mode 100644
index 000000000..ff1b91c1e
--- /dev/null
+++ b/app/code/Comave/Catalog/Plugin/Category/DataProvider.php
@@ -0,0 +1,186 @@
+<?php
+/**
+ * Copyright © Commercial Avenue
+ */
+
+namespace Comave\Catalog\Plugin\Category;
+
+use Magento\Catalog\Model\Category\DataProvider as CategoryDataProvider;
+use Magento\Framework\App\Filesystem\DirectoryList;
+use Magento\Framework\Filesystem;
+use Magento\Framework\UrlInterface;
+use Magento\Store\Model\StoreManagerInterface;
+
+/**
+ * Plugin to convert custom orientation image attributes for UI components
+ */
+class DataProvider
+{
+    /**
+     * Orientation image attributes that need UI component conversion
+     */
+    public const ORIENTATION_IMAGE_ATTRIBUTES = ['horizontal_image', 'vertical_image'];
+
+    /**
+     * MIME type mappings for common image extensions
+     */
+    public const MIME_TYPES = [
+        'jpg' => 'image/jpeg',
+        'jpeg' => 'image/jpeg',
+        'png' => 'image/png',
+        'gif' => 'image/gif',
+        'webp' => 'image/webp',
+        'svg' => 'image/svg+xml',
+        'avif' => 'image/avif'
+    ];
+
+    /**
+     * @param Filesystem $filesystem
+     * @param StoreManagerInterface $storeManager
+     */
+    public function __construct(
+        private readonly Filesystem $filesystem,
+        private readonly StoreManagerInterface $storeManager
+    ) {
+    }
+
+    /**
+     * Convert orientation image attributes to UI component format
+     *
+     * @param CategoryDataProvider $subject
+     * @param array $result
+     * @return array
+     */
+    public function afterGetData(CategoryDataProvider $subject, array $result): array
+    {
+
+
+        foreach ($result as $categoryId => &$categoryData) {
+            if (!$this->isValidCategoryData($categoryData)) {
+                continue;
+            }
+
+            foreach (self::ORIENTATION_IMAGE_ATTRIBUTES as $attributeCode) {
+                if ($this->hasImageData($categoryData, $attributeCode)) {
+                    $convertedData = $this->convertImageToUiFormat($categoryData[$attributeCode]);
+                    if ($convertedData) {
+                        $categoryData[$attributeCode] = $convertedData;
+
+                    }
+                }
+            }
+        }
+
+        return $result;
+    }
+
+    /**
+     * Check if category data is valid for processing
+     *
+     * @param mixed $categoryData
+     * @return bool
+     */
+    private function isValidCategoryData($categoryData): bool
+    {
+        return is_array($categoryData) && isset($categoryData['entity_id']);
+    }
+
+    /**
+     * Check if category has valid image data for attribute
+     *
+     * @param array $categoryData
+     * @param string $attributeCode
+     * @return bool
+     */
+    private function hasImageData(array $categoryData, string $attributeCode): bool
+    {
+        return isset($categoryData[$attributeCode]) &&
+               is_string($categoryData[$attributeCode]) &&
+               !empty($categoryData[$attributeCode]);
+    }
+
+    /**
+     * Convert image path to UI component format
+     *
+     * @param string $imagePath
+     * @return array|null
+     */
+    private function convertImageToUiFormat(string $imagePath): ?array
+    {
+        try {
+            [$relativePath, $imageUrl] = $this->resolveImagePaths($imagePath);
+
+            if (!$this->isImageFileExists($relativePath)) {
+                return null;
+            }
+
+            $fileInfo = $this->getImageFileInfo($relativePath);
+
+            return [
+                [
+                    'name' => basename($relativePath),
+                    'url' => $imageUrl,
+                    'size' => $fileInfo['size'],
+                    'type' => $fileInfo['mime_type']
+                ]
+            ];
+        } catch (\Exception $e) {
+            return null;
+        }
+    }
+
+    /**
+     * Resolve relative and absolute paths for image
+     *
+     * @param string $imagePath
+     * @return array [relativePath, imageUrl]
+     */
+    private function resolveImagePaths(string $imagePath): array
+    {
+        $baseUrl = $this->storeManager->getStore()->getBaseUrl(UrlInterface::URL_TYPE_MEDIA);
+
+        if (strpos($imagePath, 'http') === 0) {
+            $relativePath = str_replace($baseUrl, '', $imagePath);
+            $imageUrl = $imagePath;
+        } else {
+            $relativePath = $imagePath;
+            $imageUrl = $baseUrl . $relativePath;
+        }
+
+        return [$relativePath, $imageUrl];
+    }
+
+    /**
+     * Check if image file exists
+     *
+     * @param string $relativePath
+     * @return bool
+     */
+    private function isImageFileExists(string $relativePath): bool
+    {
+        $mediaDirectory = $this->filesystem->getDirectoryRead(DirectoryList::MEDIA);
+        $exists = $mediaDirectory->isExist($relativePath);
+
+
+
+        return $exists;
+    }
+
+    /**
+     * Get image file information
+     *
+     * @param string $relativePath
+     * @return array
+     */
+    private function getImageFileInfo(string $relativePath): array
+    {
+        $mediaDirectory = $this->filesystem->getDirectoryRead(DirectoryList::MEDIA);
+        $stat = $mediaDirectory->stat($relativePath);
+        $extension = strtolower(pathinfo($relativePath, PATHINFO_EXTENSION));
+
+        return [
+            'size' => $stat['size'] ?? 0,
+            'mime_type' => self::MIME_TYPES[$extension] ?? 'image/jpeg'
+        ];
+    }
+}
diff --git a/app/code/Comave/Catalog/Setup/Patch/Data/CreateCategoryOrientationImageFields.php b/app/code/Comave/Catalog/Setup/Patch/Data/CreateCategoryOrientationImageFields.php
new file mode 100644
index 000000000..5c739bc3f
--- /dev/null
+++ b/app/code/Comave/Catalog/Setup/Patch/Data/CreateCategoryOrientationImageFields.php
@@ -0,0 +1,116 @@
+<?php
+/**
+ * Copyright © Commercial Avenue
+ */
+
+namespace Comave\Catalog\Setup\Patch\Data;
+
+use Magento\Catalog\Model\Category;
+use Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface;
+use Magento\Eav\Setup\EavSetup;
+use Magento\Eav\Setup\EavSetupFactory;
+use Magento\Framework\Setup\ModuleDataSetupInterface;
+use Magento\Framework\Setup\Patch\DataPatchInterface;
+
+/**
+ * Create category horizontal and vertical image attributes
+ */
+class CreateCategoryOrientationImageFields implements DataPatchInterface
+{
+    /**
+     * Attribute names
+     */
+    public const HORIZONTAL_IMAGE_ATTRIBUTE = 'horizontal_image';
+    public const VERTICAL_IMAGE_ATTRIBUTE = 'vertical_image';
+
+    /**
+     * Attribute labels
+     */
+    public const HORIZONTAL_IMAGE_LABEL = 'Horizontal Image';
+    public const VERTICAL_IMAGE_LABEL = 'Vertical Image';
+
+    /**
+     * Attribute configuration
+     */
+    public const ATTRIBUTE_TYPE = 'varchar';
+    public const ATTRIBUTE_INPUT = 'image';
+    public const ATTRIBUTE_GROUP = 'Content';
+    public const HORIZONTAL_SORT_ORDER = 41;
+    public const VERTICAL_SORT_ORDER = 42;
+    /**
+     * @param ModuleDataSetupInterface $moduleDataSetup
+     * @param EavSetupFactory $eavSetupFactory
+     */
+    public function __construct(
+        private readonly ModuleDataSetupInterface $moduleDataSetup,
+        private readonly EavSetupFactory $eavSetupFactory
+    ) {
+    }
+
+    /**
+     * @inheritdoc
+     */
+    public function apply()
+    {
+        $this->moduleDataSetup->getConnection()->startSetup();
+
+        /** @var EavSetup $eavSetup */
+        $eavSetup = $this->eavSetupFactory->create(['setup' => $this->moduleDataSetup]);
+
+        $eavSetup->addAttribute(
+            Category::ENTITY,
+            self::HORIZONTAL_IMAGE_ATTRIBUTE,
+            [
+                'type' => self::ATTRIBUTE_TYPE,
+                'label' => self::HORIZONTAL_IMAGE_LABEL,
+                'input' => self::ATTRIBUTE_INPUT,
+                'backend' => \Comave\Catalog\Model\Category\Attribute\Backend\OrientationImage::class,
+                'required' => false,
+                'sort_order' => self::HORIZONTAL_SORT_ORDER,
+                'global' => ScopedAttributeInterface::SCOPE_STORE,
+                'group' => self::ATTRIBUTE_GROUP,
+                'used_in_product_listing' => false,
+                'visible_on_front' => false,
+                'user_defined' => false,
+                'default' => '',
+            ]
+        );
+
+        $eavSetup->addAttribute(
+            Category::ENTITY,
+            self::VERTICAL_IMAGE_ATTRIBUTE,
+            [
+                'type' => self::ATTRIBUTE_TYPE,
+                'label' => self::VERTICAL_IMAGE_LABEL,
+                'input' => self::ATTRIBUTE_INPUT,
+                'backend' => \Comave\Catalog\Model\Category\Attribute\Backend\OrientationImage::class,
+                'required' => false,
+                'sort_order' => self::VERTICAL_SORT_ORDER,
+                'global' => ScopedAttributeInterface::SCOPE_STORE,
+                'group' => self::ATTRIBUTE_GROUP,
+                'used_in_product_listing' => false,
+                'visible_on_front' => false,
+                'user_defined' => false,
+                'default' => '',
+            ]
+        );
+
+        $this->moduleDataSetup->getConnection()->endSetup();
+    }
+
+    /**
+     * @inheritdoc
+     */
+    public static function getDependencies()
+    {
+        return [];
+    }
+
+    /**
+     * @inheritdoc
+     */
+    public function getAliases()
+    {
+        return [];
+    }
+}
diff --git a/app/code/Comave/Catalog/etc/di.xml b/app/code/Comave/Catalog/etc/di.xml
index d6bf244cd..d36a36437 100644
--- a/app/code/Comave/Catalog/etc/di.xml
+++ b/app/code/Comave/Catalog/etc/di.xml
@@ -67,4 +67,23 @@
                 type="Comave\Catalog\Plugin\ProductVariantImageInheritancePlugin"
                 sortOrder="10"/>
     </type>
+
+    <type name="Comave\Catalog\Model\Category\Attribute\Backend\OrientationImage">
+        <arguments>
+            <argument name="fileUploaderFactory" xsi:type="object">Magento\MediaStorage\Model\File\UploaderFactory</argument>
+            <argument name="filesystem" xsi:type="object">Magento\Framework\Filesystem</argument>
+            <argument name="storeManager" xsi:type="object">Magento\Store\Model\StoreManagerInterface</argument>
+        </arguments>
+    </type>
+
+    <type name="Comave\Catalog\Plugin\Category\DataProvider">
+        <arguments>
+            <argument name="filesystem" xsi:type="object">Magento\Framework\Filesystem</argument>
+            <argument name="storeManager" xsi:type="object">Magento\Store\Model\StoreManagerInterface</argument>
+        </arguments>
+    </type>
+
+    <type name="Magento\Catalog\Model\Category\DataProvider">
+        <plugin name="comave_catalog_category_data_provider" type="Comave\Catalog\Plugin\Category\DataProvider" sortOrder="10"/>
+    </type>
 </config>
diff --git a/app/code/Comave/Catalog/view/adminhtml/ui_component/category_form.xml b/app/code/Comave/Catalog/view/adminhtml/ui_component/category_form.xml
new file mode 100644
index 000000000..a5a63a3ec
--- /dev/null
+++ b/app/code/Comave/Catalog/view/adminhtml/ui_component/category_form.xml
@@ -0,0 +1,63 @@
+<?xml version="1.0" encoding="UTF-8"?>
+<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
+    <fieldset name="content" sortOrder="10">
+        <field name="horizontal_image" sortOrder="41" formElement="imageUploader">
+            <argument name="data" xsi:type="array">
+                <item name="config" xsi:type="array">
+                    <item name="source" xsi:type="string">category</item>
+                </item>
+            </argument>
+            <settings>
+                <elementTmpl>ui/form/element/uploader/image</elementTmpl>
+                <dataType>string</dataType>
+                <label translate="true">Horizontal Image</label>
+                <visible>true</visible>
+                <required>false</required>
+            </settings>
+            <formElements>
+                <imageUploader>
+                    <settings>
+                        <required>false</required>
+                        <uploaderConfig>
+                            <param xsi:type="url" name="url" path="comave_catalog/category_image/upload"/>
+                        </uploaderConfig>
+                        <previewTmpl>Magento_Catalog/image-preview</previewTmpl>
+                        <openDialogTitle>Media Gallery</openDialogTitle>
+                        <initialMediaGalleryOpenSubpath>catalog/category</initialMediaGalleryOpenSubpath>
+                        <allowedExtensions>jpg jpeg gif png webp avif jfif svg</allowedExtensions>
+                        <maxFileSize>4194304</maxFileSize>
+                    </settings>
+                </imageUploader>
+            </formElements>
+        </field>
+        <field name="vertical_image" sortOrder="42" formElement="imageUploader">
+            <argument name="data" xsi:type="array">
+                <item name="config" xsi:type="array">
+                    <item name="source" xsi:type="string">category</item>
+                </item>
+            </argument>
+            <settings>
+                <elementTmpl>ui/form/element/uploader/image</elementTmpl>
+                <dataType>string</dataType>
+                <label translate="true">Vertical Image</label>
+                <visible>true</visible>
+                <required>false</required>
+            </settings>
+            <formElements>
+                <imageUploader>
+                    <settings>
+                        <required>false</required>
+                        <uploaderConfig>
+                            <param xsi:type="url" name="url" path="comave_catalog/category_image/upload"/>
+                        </uploaderConfig>
+                        <previewTmpl>Magento_Catalog/image-preview</previewTmpl>
+                        <openDialogTitle>Media Gallery</openDialogTitle>
+                        <initialMediaGalleryOpenSubpath>catalog/category</initialMediaGalleryOpenSubpath>
+                        <allowedExtensions>jpg jpeg gif png webp avif jfif svg</allowedExtensions>
+                        <maxFileSize>4194304</maxFileSize>
+                    </settings>
+                </imageUploader>
+            </formElements>
+        </field>
+    </fieldset>
+</form>
diff --git a/app/code/Comave/CatalogGraphQl/Model/Resolver/Category/OrientationImage.php b/app/code/Comave/CatalogGraphQl/Model/Resolver/Category/OrientationImage.php
new file mode 100644
index 000000000..9fa7d6d31
--- /dev/null
+++ b/app/code/Comave/CatalogGraphQl/Model/Resolver/Category/OrientationImage.php
@@ -0,0 +1,121 @@
+<?php
+/**
+ * Copyright © Commercial Avenue
+ */
+
+namespace Comave\CatalogGraphQl\Model\Resolver\Category;
+
+use Magento\Catalog\Model\Category;
+use Magento\Framework\Exception\LocalizedException;
+use Magento\Framework\GraphQl\Config\Element\Field;
+use Magento\Framework\GraphQl\Query\ResolverInterface;
+use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
+use Magento\Framework\UrlInterface;
+use Magento\Store\Model\StoreManagerInterface;
+
+/**
+ * Unified resolver for category orientation images (horizontal/vertical)
+ */
+class OrientationImage implements ResolverInterface
+{
+    /**
+     * URL detection
+     */
+    public const HTTP_PROTOCOL_PREFIX = 'http';
+
+    /**
+     * @param StoreManagerInterface $storeManager
+     */
+    public function __construct(
+        private readonly StoreManagerInterface $storeManager
+    ) {
+    }
+
+    /**
+     * Resolve orientation image URL
+     *
+     * @param Field $field
+     * @param mixed $context
+     * @param ResolveInfo $info
+     * @param array|null $value
+     * @param array|null $args
+     * @return string|null
+     * @throws LocalizedException
+     */
+    public function resolve(Field $field, $context, ResolveInfo $info, array $value = null, array $args = null): ?string
+    {
+        if (!isset($value['model']) || !$value['model'] instanceof Category) {
+            throw new LocalizedException(__('"model" value should be specified'));
+        }
+
+        /** @var Category $category */
+        $category = $value['model'];
+        $attributeName = $field->getName();
+        $imageValue = $category->getData($attributeName);
+
+        if (!$imageValue) {
+            return null;
+        }
+
+        $imagePath = $this->extractImagePath($imageValue);
+
+        if (!$imagePath) {
+            return null;
+        }
+        return $this->buildImageUrl($imagePath);
+    }
+
+    /**
+     * Build full image URL from relative path
+     *
+     * @param string $imagePath
+     * @return string|null
+     */
+    private function buildImageUrl(string $imagePath): ?string
+    {
+        try {
+            if (strpos($imagePath, self::HTTP_PROTOCOL_PREFIX) === 0) {
+                return $imagePath;
+            }
+
+            $mediaUrl = $this->storeManager->getStore()->getBaseUrl(UrlInterface::URL_TYPE_MEDIA);
+            return $mediaUrl . ltrim($imagePath, '/');
+        } catch (\Exception $e) {
+            return null;
+        }
+    }
+
+    /**
+     * Extract image path from value (handles both string and array formats)
+     *
+     * @param mixed $imageValue
+     * @return string|null
+     */
+    private function extractImagePath($imageValue): ?string
+    {
+        if (is_string($imageValue)) {
+            return $imageValue;
+        }
+
+        if (is_array($imageValue) && isset($imageValue[0])) {
+            $firstItem = $imageValue[0];
+
+            if (isset($firstItem['file'])) {
+                return $firstItem['file'];
+            }
+
+            if (isset($firstItem['url'])) {
+                $url = $firstItem['url'];
+                $mediaUrl = $this->storeManager->getStore()->getBaseUrl(\Magento\Framework\UrlInterface::URL_TYPE_MEDIA);
+                $relativePath = str_replace($mediaUrl, '', $url);
+                return ltrim($relativePath, '/');
+            }
+
+            if (isset($firstItem['name'])) {
+                return $firstItem['name'];
+            }
+        }
+
+        return null;
+    }
+}
diff --git a/app/code/Comave/CatalogGraphQl/etc/di.xml b/app/code/Comave/CatalogGraphQl/etc/di.xml
index b7e565f4f..ed48499a7 100644
--- a/app/code/Comave/CatalogGraphQl/etc/di.xml
+++ b/app/code/Comave/CatalogGraphQl/etc/di.xml
@@ -9,4 +9,10 @@
                 type="Comave\CatalogGraphQl\Plugin\RemoveDisabledProducts"
                 sortOrder="1"/>
     </type>
+
+    <type name="Comave\CatalogGraphQl\Model\Resolver\Category\OrientationImage">
+        <arguments>
+            <argument name="storeManager" xsi:type="object">Magento\Store\Model\StoreManagerInterface</argument>
+        </arguments>
+    </type>
 </config>
diff --git a/app/code/Comave/CatalogGraphQl/etc/schema.graphqls b/app/code/Comave/CatalogGraphQl/etc/schema.graphqls
index 37d1df137..46b15047d 100644
--- a/app/code/Comave/CatalogGraphQl/etc/schema.graphqls
+++ b/app/code/Comave/CatalogGraphQl/etc/schema.graphqls
@@ -58,4 +58,26 @@ type VisibleFrontendAttribute {
 input ProductAttributeFilterInput {
     brand: FilterEqualTypeInput @doc(description: "Product Data filter with Brand Value")
     status: FilterEqualTypeInput @doc(description: "Filter products by status (1 for Enabled, 2 for Disabled).")
-}
\ No newline at end of file
+}
+
+interface CategoryInterface {
+    horizontal_image: String
+        @doc(description: "Category horizontal image URL")
+        @resolver(
+            class: "\\Comave\\CatalogGraphQl\\Model\\Resolver\\Category\\OrientationImage"
+        )
+    vertical_image: String
+        @doc(description: "Category vertical image URL")
+        @resolver(
+            class: "\\Comave\\CatalogGraphQl\\Model\\Resolver\\Category\\OrientationImage"
+        )
+}
+
+input ProductAttributeFilterInput {
+    brand: FilterEqualTypeInput
+        @doc(description: "Product Data filter with Brand Value")
+    status: FilterEqualTypeInput
+        @doc(
+            description: "Filter products by status (1 for Enabled, 2 for Disabled)."
+        )
+}
