<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\CatalogGraphQl\Model\Resolver\Products">
        <plugin name="apply_status_filter_plugin" type="Comave\CatalogGraphQl\Plugin\FilterEnabledProducts" />
    </type>
    <type name="Magento\CatalogGraphQl\Model\Resolver\Category\Products">
        <plugin name="removedisabled"
                type="Comave\CatalogGraphQl\Plugin\RemoveDisabledProducts"
                sortOrder="1"/>
    </type>

    <type name="Comave\CatalogGraphQl\Model\Resolver\Category\OrientationImage">
        <arguments>
            <argument name="storeManager" xsi:type="object">Magento\Store\Model\StoreManagerInterface</argument>
            <argument name="logger" xsi:type="object">Psr\Log\LoggerInterface</argument>
        </arguments>
    </type>

    <type name="Comave\CatalogGraphQl\Model\Service\CategoryOrientationImageProcessor">
        <arguments>
            <argument name="imageProcessor" xsi:type="object">Magento\Framework\Api\ImageProcessorInterface</argument>
            <argument name="imageContentFactory" xsi:type="object">Magento\Framework\Api\Data\ImageContentInterfaceFactory</argument>
            <argument name="imageUploaderFactory" xsi:type="object">Magento\Catalog\Model\ImageUploaderFactory</argument>
            <argument name="filesystem" xsi:type="object">Magento\Framework\Filesystem</argument>
            <argument name="storeManager" xsi:type="object">Magento\Store\Model\StoreManagerInterface</argument>
            <argument name="logger" xsi:type="object">Psr\Log\LoggerInterface</argument>
        </arguments>
    </type>

    <type name="Comave\CatalogGraphQl\Model\Resolver\Category\UpdateOrientationImage">
        <arguments>
            <argument name="imageProcessor" xsi:type="object">Comave\CatalogGraphQl\Model\Service\CategoryOrientationImageProcessor</argument>
            <argument name="categoryRepository" xsi:type="object">Magento\Catalog\Api\CategoryRepositoryInterface</argument>
            <argument name="logger" xsi:type="object">Psr\Log\LoggerInterface</argument>
        </arguments>
    </type>
</config>
