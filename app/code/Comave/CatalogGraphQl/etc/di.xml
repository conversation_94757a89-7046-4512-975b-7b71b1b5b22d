<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\CatalogGraphQl\Model\Resolver\Products">
        <plugin name="apply_status_filter_plugin" type="Comave\CatalogGraphQl\Plugin\FilterEnabledProducts" />
    </type>
    <type name="Magento\CatalogGraphQl\Model\Resolver\Category\Products">
        <plugin name="removedisabled"
                type="Comave\CatalogGraphQl\Plugin\RemoveDisabledProducts"
                sortOrder="1"/>
    </type>

    <type name="Comave\CatalogGraphQl\Model\Resolver\Category\OrientationImage">
        <arguments>
            <argument name="storeManager" xsi:type="object">Magento\Store\Model\StoreManagerInterface</argument>
        </arguments>
    </type>
</config>
