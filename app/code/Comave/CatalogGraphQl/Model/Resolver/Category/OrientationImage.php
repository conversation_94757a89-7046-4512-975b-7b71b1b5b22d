<?php

namespace Comave\CatalogGraphQl\Model\Resolver\Category;

use Magento\Catalog\Model\Category;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\Framework\UrlInterface;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

/**
 * Unified resolver for category orientation images (horizontal/vertical)
 */
class OrientationImage implements ResolverInterface
{
    /**
     * URL detection
     */
    public const HTTP_PROTOCOL_PREFIX = 'http';

    /**
     * @param StoreManagerInterface $storeManager
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly StoreManagerInterface $storeManager,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Resolve orientation image URL
     *
     * @param Field $field
     * @param mixed $context
     * @param ResolveInfo $info
     * @param array|null $value
     * @param array|null $args
     * @return string|null
     * @throws LocalizedException
     */
    public function resolve(Field $field, $context, ResolveInfo $info, array $value = null, array $args = null): ?string
    {
        if (!isset($value['model']) || !$value['model'] instanceof Category) {
            throw new LocalizedException(__('"model" value should be specified'));
        }

        /** @var Category $category */
        $category = $value['model'];
        $attributeName = $field->getName();
        $imageValue = $category->getData($attributeName);

        if (!$imageValue) {
            return null;
        }

        $imagePath = $this->extractImagePath($imageValue);

        if (!$imagePath) {
            return null;
        }
        return $this->buildImageUrl($imagePath);
    }

    /**
     * Build full image URL from relative path
     *
     * @param string $imagePath
     * @return string|null
     */
    private function buildImageUrl(string $imagePath): ?string
    {
        try {
            if (strpos($imagePath, self::HTTP_PROTOCOL_PREFIX) === 0) {
                return $imagePath;
            }

            $mediaUrl = $this->storeManager->getStore()->getBaseUrl(UrlInterface::URL_TYPE_MEDIA);
            return $mediaUrl . ltrim($imagePath, '/');
        } catch (\Exception $e) {
            $this->logger->error('Failed to build image URL for GraphQL', [
                'exception' => $e->getMessage(),
                'image_path' => $imagePath,
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * Extract image path from value (handles both string and array formats)
     *
     * @param mixed $imageValue
     * @return string|null
     */
    private function extractImagePath($imageValue): ?string
    {
        if (is_string($imageValue)) {
            return $imageValue;
        }

        if (is_array($imageValue) && isset($imageValue[0])) {
            $firstItem = $imageValue[0];

            if (isset($firstItem['file'])) {
                return $firstItem['file'];
            }

            if (isset($firstItem['url'])) {
                $url = $firstItem['url'];
                $mediaUrl = $this->storeManager->getStore()->getBaseUrl(\Magento\Framework\UrlInterface::URL_TYPE_MEDIA);
                $relativePath = str_replace($mediaUrl, '', $url);
                return ltrim($relativePath, '/');
            }

            if (isset($firstItem['name'])) {
                return $firstItem['name'];
            }
        }

        return null;
    }
}
