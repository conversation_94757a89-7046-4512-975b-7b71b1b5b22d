<?php

namespace Comave\CatalogGraphQl\Model\Resolver\Category;

use Comave\CatalogGraphQl\Model\Service\CategoryOrientationImageProcessor;
use Magento\Catalog\Api\CategoryRepositoryInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Filesystem;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Framework\GraphQl\Exception\GraphQlNoSuchEntityException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

/**
 * GraphQL resolver for updating category orientation images via base64 upload
 */
class UpdateOrientationImage implements ResolverInterface
{
    /**
     * @param CategoryOrientationImageProcessor $imageProcessor
     * @param CategoryRepositoryInterface $categoryRepository
     * @param Filesystem $filesystem
     * @param StoreManagerInterface $storeManager
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly CategoryOrientationImageProcessor $imageProcessor,
        private readonly CategoryRepositoryInterface $categoryRepository,
        private readonly Filesystem $filesystem,
        private readonly StoreManagerInterface $storeManager,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Resolve GraphQL mutation for category orientation image upload
     *
     * @param Field $field
     * @param mixed $context
     * @param ResolveInfo $info
     * @param array|null $value
     * @param array|null $args
     * @return array
     * @throws GraphQlInputException
     * @throws GraphQlNoSuchEntityException
     */
    public function resolve(Field $field, $context, ResolveInfo $info, array $value = null, array $args = null): array
    {
        try {
            $input = $args['input'] ?? [];
            $this->validateInput($input);

            $categoryId = (int) $input['category_id'];
            $orientation = $this->imageProcessor->convertOrientationEnum($input['orientation']);
            $imageFileData = $input['image_file'];

            // Load and validate category
            $category = $this->loadCategory($categoryId);

            // Process the base64 image using the same flow as admin upload
            $result = $this->imageProcessor->processBase64Image($imageFileData, $orientation);

            // Update the category with the new image path
            $this->updateCategoryImage($category, $orientation, $result['image_path'], $result['image_url'], $result['tmp_file_path']);

            return [
                'success' => true,
                'message' => 'Category ' . $orientation . ' image uploaded successfully',
                'image_url' => $result['image_url'],
                'image_path' => $result['image_path']
            ];

        } catch (LocalizedException $e) {
            $this->logger->error('GraphQL category orientation image upload failed', [
                'exception' => $e->getMessage(),
                'category_id' => $input['category_id'] ?? 'unknown',
                'orientation' => $input['orientation'] ?? 'unknown',
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => $e->getMessage(),
                'image_url' => null,
                'image_path' => null
            ];

        } catch (\Exception $e) {
            $this->logger->error('Unexpected error in GraphQL category orientation image upload', [
                'exception' => $e->getMessage(),
                'category_id' => $input['category_id'] ?? 'unknown',
                'orientation' => $input['orientation'] ?? 'unknown',
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'An unexpected error occurred during image upload',
                'image_url' => null,
                'image_path' => null
            ];
        }
    }

    /**
     * Validate GraphQL input parameters
     *
     * @param array $input
     * @throws GraphQlInputException
     */
    private function validateInput(array $input): void
    {
        if (empty($input['category_id'])) {
            throw new GraphQlInputException(__('Category ID is required'));
        }

        if (empty($input['orientation'])) {
            throw new GraphQlInputException(__('Orientation is required'));
        }

        if (empty($input['image_file'])) {
            throw new GraphQlInputException(__('Image file data is required'));
        }

        $imageFile = $input['image_file'];
        if (empty($imageFile['name'])) {
            throw new GraphQlInputException(__('Image file name is required'));
        }

        if (empty($imageFile['base64_encoded_file'])) {
            throw new GraphQlInputException(__('Base64 encoded file content is required'));
        }

        if (empty($imageFile['type'])) {
            throw new GraphQlInputException(__('Image file type is required'));
        }
    }

    /**
     * Load and validate category
     *
     * @param int $categoryId
     * @return \Magento\Catalog\Api\Data\CategoryInterface
     * @throws GraphQlNoSuchEntityException
     */
    private function loadCategory(int $categoryId): \Magento\Catalog\Api\Data\CategoryInterface
    {
        try {
            return $this->categoryRepository->get($categoryId);
        } catch (NoSuchEntityException $e) {
            throw new GraphQlNoSuchEntityException(__('Category with ID %1 not found', $categoryId));
        }
    }

    /**
     * Update category with new image path using direct path assignment
     *
     * @param \Magento\Catalog\Api\Data\CategoryInterface $category
     * @param string $orientation
     * @param string $imagePath
     * @param string $imageUrl
     * @param string $tmpFilePath
     * @throws LocalizedException
     */
    private function updateCategoryImage(
        \Magento\Catalog\Api\Data\CategoryInterface $category,
        string $orientation,
        string $imagePath,
        string $imageUrl,
        string $tmpFilePath
    ): void {
        $attributeName = $orientation . '_image';

        // Set the final image path directly - this will be handled by the backend model's string path logic
        $category->setData($attributeName, $imagePath);

        // Also set the additional UI data for consistency with admin uploads
        $imageData = [
            [
                'file' => $imagePath,
                'url' => $imageUrl,
                'name' => basename($imagePath),
                'size' => $this->getFileSize($imagePath),
                'type' => $this->getMimeType($imagePath)
            ]
        ];
        $category->setData('_additional_data_' . $attributeName, $imageData);

        try {
            $this->categoryRepository->save($category);
        } catch (\Exception $e) {
            throw new LocalizedException(__('Failed to save category: %1', $e->getMessage()));
        }
    }

    /**
     * Get file size for image
     *
     * @param string $imagePath
     * @return int
     */
    private function getFileSize(string $imagePath): int
    {
        try {
            $mediaDirectory = $this->filesystem->getDirectoryRead(\Magento\Framework\App\Filesystem\DirectoryList::MEDIA);
            $stat = $mediaDirectory->stat($imagePath);
            return $stat['size'] ?? 0;
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Get MIME type for image
     *
     * @param string $imagePath
     * @return string
     */
    private function getMimeType(string $imagePath): string
    {
        $extension = strtolower(pathinfo($imagePath, PATHINFO_EXTENSION));
        $mimeTypes = [
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'gif' => 'image/gif',
            'webp' => 'image/webp',
            'svg' => 'image/svg+xml',
            'avif' => 'image/avif'
        ];

        return $mimeTypes[$extension] ?? 'image/jpeg';
    }
}
