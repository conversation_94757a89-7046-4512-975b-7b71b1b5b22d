<?php

namespace Comave\CatalogGraphQl\Model\Service;

use Magento\Catalog\Model\ImageUploaderFactory;
use Magento\Framework\Api\Data\ImageContentInterfaceFactory;
use Magento\Framework\Api\ImageProcessorInterface;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Filesystem;
use Magento\Framework\UrlInterface;
use Magento\MediaStorage\Model\File\Uploader;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

/**
 * Service to process base64 category orientation images using the same flow as admin upload
 */
class CategoryOrientationImageProcessor
{
    /**
     * Supported orientations
     */
    public const SUPPORTED_ORIENTATIONS = ['horizontal', 'vertical'];

    /**
     * Path templates - same as Upload controller
     */
    public const BASE_TMP_PATH_TEMPLATE = 'catalog/category/%s';
    public const BASE_PATH_TEMPLATE = 'catalog/category/%s';

    /**
     * Allowed file extensions - same as Upload controller
     */
    public const ALLOWED_EXTENSIONS = ['gif', 'jpg', 'png', 'jpeg', 'webp', 'avif', 'jfif', 'svg'];

    /**
     * @param ImageProcessorInterface $imageProcessor
     * @param ImageContentInterfaceFactory $imageContentFactory
     * @param ImageUploaderFactory $imageUploaderFactory
     * @param Filesystem $filesystem
     * @param StoreManagerInterface $storeManager
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly ImageProcessorInterface $imageProcessor,
        private readonly ImageContentInterfaceFactory $imageContentFactory,
        private readonly ImageUploaderFactory $imageUploaderFactory,
        private readonly Filesystem $filesystem,
        private readonly StoreManagerInterface $storeManager,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Process base64 image data and save it using the same flow as admin upload
     *
     * @param array $imageData
     * @param string $orientation
     * @return array
     * @throws LocalizedException
     */
    public function processBase64Image(array $imageData, string $orientation): array
    {
        try {
            if (!$this->isValidOrientation($orientation)) {
                throw new LocalizedException(__('Invalid orientation type: %1', $orientation));
            }

            // Step 1: Convert base64 to physical file and save to tmp using ImageUploader (same as admin)
            $tempResult = $this->saveBase64ToTmpDir($imageData, $orientation);

            // Step 2: Move from temp to final location using the same ImageUploader as admin
            $finalImagePath = $this->moveToFinalLocation($tempResult['file'], $orientation);

            // Step 3: Generate the final URL
            $imageUrl = $this->generateImageUrl($finalImagePath);

            return [
                'success' => true,
                'image_path' => $finalImagePath,
                'image_url' => $imageUrl,
                'message' => 'Image uploaded successfully'
            ];

        } catch (\Exception $e) {
            $this->logger->error('Category orientation image processing failed in GraphQL', [
                'exception' => $e->getMessage(),
                'orientation' => $orientation,
                'image_name' => $imageData['name'] ?? 'unknown',
                'trace' => $e->getTraceAsString()
            ]);

            throw new LocalizedException(__('Failed to process image: %1', $e->getMessage()));
        }
    }

    /**
     * Convert base64 data to physical file and save to tmp dir using ImageUploader (same as admin)
     *
     * @param array $imageData
     * @param string $orientation
     * @return array
     * @throws LocalizedException
     */
    private function saveBase64ToTmpDir(array $imageData, string $orientation): array
    {
        // Step 1: Convert base64 to physical file
        $tempFilePath = $this->createTempFileFromBase64($imageData);

        // Step 2: Use ImageUploader to save to tmp dir (same as admin controller)
        $imageUploader = $this->createImageUploader($orientation);

        // Simulate file upload by creating a temporary file structure
        $result = $this->saveFileToTmpDirFromPath($imageUploader, $tempFilePath, $imageData['name']);

        // Clean up the temporary file
        if (file_exists($tempFilePath)) {
            unlink($tempFilePath);
        }

        return $result;
    }

    /**
     * Create temporary file from base64 data
     *
     * @param array $imageData
     * @return string
     * @throws LocalizedException
     */
    private function createTempFileFromBase64(array $imageData): string
    {
        $base64Data = $imageData['base64_encoded_file'];
        $fileName = $imageData['name'];

        // Decode base64 data
        $fileContent = base64_decode($base64Data);
        if ($fileContent === false) {
            throw new LocalizedException(__('Invalid base64 data'));
        }

        // Create temporary file
        $tempDir = sys_get_temp_dir();
        $tempFilePath = $tempDir . '/' . uniqid('graphql_upload_') . '_' . $fileName;

        if (file_put_contents($tempFilePath, $fileContent) === false) {
            throw new LocalizedException(__('Failed to create temporary file'));
        }

        return $tempFilePath;
    }

    /**
     * Save file to tmp dir using ImageUploader from physical file path
     *
     * @param \Magento\Catalog\Model\ImageUploader $imageUploader
     * @param string $filePath
     * @param string $originalName
     * @return array
     * @throws LocalizedException
     */
    private function saveFileToTmpDirFromPath(\Magento\Catalog\Model\ImageUploader $imageUploader, string $filePath, string $originalName): array
    {
        $mediaDirectory = $this->filesystem->getDirectoryWrite(DirectoryList::MEDIA);
        $baseTmpPath = $imageUploader->getBaseTmpPath();

        // Create the tmp directory if it doesn't exist
        $tmpDir = $mediaDirectory->getAbsolutePath($baseTmpPath);
        if (!is_dir($tmpDir)) {
            mkdir($tmpDir, 0777, true);
        }

        // Generate unique filename
        $pathInfo = pathinfo($originalName);
        $baseName = $pathInfo['filename'];
        $extension = $pathInfo['extension'] ?? '';

        // Create dispersed path (same as Magento's file dispersion)
        $dispersionPath = Uploader::getDispretionPath($baseName);
        $fileName = Uploader::getCorrectFileName($originalName);

        $destinationDir = $tmpDir . $dispersionPath;
        if (!is_dir($destinationDir)) {
            mkdir($destinationDir, 0777, true);
        }

        $destinationFile = $destinationDir . DIRECTORY_SEPARATOR . $fileName;

        // Copy file to destination
        if (!copy($filePath, $destinationFile)) {
            throw new LocalizedException(__('Failed to copy file to tmp directory'));
        }

        return [
            'file' => $dispersionPath . DIRECTORY_SEPARATOR . $fileName,
            'name' => $fileName,
            'path' => $destinationFile,
            'tmp_name' => $destinationFile
        ];
    }

    /**
     * Move image from temp to final location using ImageUploader (same as admin)
     *
     * @param string $tempImagePath
     * @param string $orientation
     * @return string
     * @throws LocalizedException
     */
    private function moveToFinalLocation(string $tempImagePath, string $orientation): string
    {
        $imageUploader = $this->createImageUploader($orientation);

        // Move from tmp to final location
        $result = $imageUploader->moveFileFromTmp($tempImagePath);

        return $result;
    }

    /**
     * Create image uploader for specific orientation (same as Upload controller)
     *
     * @param string $orientation
     * @return \Magento\Catalog\Model\ImageUploader
     */
    private function createImageUploader(string $orientation): \Magento\Catalog\Model\ImageUploader
    {
        $baseTmpPath = sprintf(self::BASE_TMP_PATH_TEMPLATE, $orientation);
        $basePath = sprintf(self::BASE_PATH_TEMPLATE, $orientation);

        return $this->imageUploaderFactory->create([
            'baseTmpPath' => $baseTmpPath,
            'basePath' => $basePath,
            'allowedExtensions' => self::ALLOWED_EXTENSIONS
        ]);
    }

    /**
     * Generate final image URL
     *
     * @param string $imagePath
     * @return string
     */
    private function generateImageUrl(string $imagePath): string
    {
        $baseUrl = $this->storeManager->getStore()->getBaseUrl(UrlInterface::URL_TYPE_MEDIA);
        return $baseUrl . ltrim($imagePath, '/');
    }

    /**
     * Check if orientation is valid
     *
     * @param string $orientation
     * @return bool
     */
    private function isValidOrientation(string $orientation): bool
    {
        return in_array($orientation, self::SUPPORTED_ORIENTATIONS, true);
    }

    /**
     * Convert GraphQL enum to lowercase string
     *
     * @param string $orientationEnum
     * @return string
     */
    public function convertOrientationEnum(string $orientationEnum): string
    {
        return strtolower($orientationEnum);
    }
}
