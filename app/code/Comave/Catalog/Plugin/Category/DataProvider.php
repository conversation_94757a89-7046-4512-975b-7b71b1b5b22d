<?php

namespace Comave\Catalog\Plugin\Category;

use Magento\Catalog\Model\Category\DataProvider as CategoryDataProvider;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Filesystem;
use Magento\Framework\UrlInterface;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

/**
 * Plugin to convert custom orientation image attributes for UI components
 */
class DataProvider
{
    /**
     * Orientation image attributes that need UI component conversion
     */
    public const ORIENTATION_IMAGE_ATTRIBUTES = ['horizontal_image', 'vertical_image'];

    /**
     * MIME type mappings for common image extensions
     */
    public const MIME_TYPES = [
        'jpg' => 'image/jpeg',
        'jpeg' => 'image/jpeg',
        'png' => 'image/png',
        'gif' => 'image/gif',
        'webp' => 'image/webp',
        'svg' => 'image/svg+xml',
        'avif' => 'image/avif'
    ];

    /**
     * @param Filesystem $filesystem
     * @param StoreManagerInterface $storeManager
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly Filesystem $filesystem,
        private readonly StoreManagerInterface $storeManager,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Convert orientation image attributes to UI component format
     *
     * @param CategoryDataProvider $subject
     * @param array $result
     * @return array
     */
    public function afterGetData(CategoryDataProvider $subject, array $result): array
    {
        $this->logger->info('DataProvider afterGetData called', [
            'result_keys' => array_keys($result),
            'result_count' => count($result)
        ]);

        foreach ($result as $categoryId => &$categoryData) {
            if (!$this->isValidCategoryData($categoryData)) {
                $this->logger->info('Invalid category data', ['category_id' => $categoryId]);
                continue;
            }

            $this->logger->info('Processing category data', [
                'category_id' => $categoryId,
                'has_horizontal_image' => isset($categoryData['horizontal_image']),
                'has_vertical_image' => isset($categoryData['vertical_image']),
                'horizontal_image_value' => $categoryData['horizontal_image'] ?? 'not_set',
                'vertical_image_value' => $categoryData['vertical_image'] ?? 'not_set',
                'has_additional_horizontal' => isset($categoryData['_additional_data_horizontal_image']),
                'has_additional_vertical' => isset($categoryData['_additional_data_vertical_image'])
            ]);

            foreach (self::ORIENTATION_IMAGE_ATTRIBUTES as $attributeCode) {
                if ($this->hasImageData($categoryData, $attributeCode)) {
                    $this->logger->info('Converting image data for UI', [
                        'category_id' => $categoryId,
                        'attribute_code' => $attributeCode,
                        'original_value' => $categoryData[$attributeCode]
                    ]);

                    $convertedData = $this->convertImageToUiFormat($categoryData[$attributeCode]);
                    if ($convertedData) {
                        $this->logger->info('Image conversion successful', [
                            'category_id' => $categoryId,
                            'attribute_code' => $attributeCode,
                            'converted_data' => $convertedData
                        ]);
                        $categoryData[$attributeCode] = $convertedData;
                    } else {
                        $this->logger->warning('Image conversion failed', [
                            'category_id' => $categoryId,
                            'attribute_code' => $attributeCode,
                            'original_value' => $categoryData[$attributeCode]
                        ]);
                    }
                } else {
                    $this->logger->info('No image data found', [
                        'category_id' => $categoryId,
                        'attribute_code' => $attributeCode,
                        'value_type' => gettype($categoryData[$attributeCode] ?? null),
                        'value' => $categoryData[$attributeCode] ?? 'not_set'
                    ]);
                }
            }
        }

        return $result;
    }

    /**
     * Check if category data is valid for processing
     *
     * @param mixed $categoryData
     * @return bool
     */
    private function isValidCategoryData($categoryData): bool
    {
        return is_array($categoryData) && isset($categoryData['entity_id']);
    }

    /**
     * Check if category has valid image data for attribute
     *
     * @param array $categoryData
     * @param string $attributeCode
     * @return bool
     */
    private function hasImageData(array $categoryData, string $attributeCode): bool
    {
        return isset($categoryData[$attributeCode]) &&
               is_string($categoryData[$attributeCode]) &&
               !empty($categoryData[$attributeCode]);
    }

    /**
     * Convert image path to UI component format
     *
     * @param string $imagePath
     * @return array|null
     */
    private function convertImageToUiFormat(string $imagePath): ?array
    {
        try {
            $this->logger->info('Starting image conversion to UI format', [
                'image_path' => $imagePath
            ]);

            [$relativePath, $imageUrl] = $this->resolveImagePaths($imagePath);

            $this->logger->info('Image paths resolved', [
                'relative_path' => $relativePath,
                'image_url' => $imageUrl
            ]);

            if (!$this->isImageFileExists($relativePath)) {
                $this->logger->warning('Image file does not exist', [
                    'relative_path' => $relativePath,
                    'image_url' => $imageUrl
                ]);
                return null;
            }

            $fileInfo = $this->getImageFileInfo($relativePath);

            $this->logger->info('Image file info retrieved', [
                'file_info' => $fileInfo
            ]);

            $result = [
                [
                    'name' => basename($relativePath),
                    'url' => $imageUrl,
                    'size' => $fileInfo['size'],
                    'type' => $fileInfo['mime_type']
                ]
            ];

            $this->logger->info('Image conversion completed successfully', [
                'result' => $result
            ]);

            return $result;
        } catch (\Exception $e) {
            $this->logger->error('Failed to convert image to UI format', [
                'exception' => $e->getMessage(),
                'image_path' => $imagePath,
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * Resolve relative and absolute paths for image
     *
     * @param string $imagePath
     * @return array [relativePath, imageUrl]
     */
    private function resolveImagePaths(string $imagePath): array
    {
        $baseUrl = $this->storeManager->getStore()->getBaseUrl(UrlInterface::URL_TYPE_MEDIA);

        if (strpos($imagePath, 'http') === 0) {
            $relativePath = str_replace($baseUrl, '', $imagePath);
            $imageUrl = $imagePath;
        } else {
            $relativePath = $imagePath;
            $imageUrl = $baseUrl . $relativePath;
        }

        return [$relativePath, $imageUrl];
    }

    /**
     * Check if image file exists
     *
     * @param string $relativePath
     * @return bool
     */
    private function isImageFileExists(string $relativePath): bool
    {
        $mediaDirectory = $this->filesystem->getDirectoryRead(DirectoryList::MEDIA);
        $exists = $mediaDirectory->isExist($relativePath);

        $this->logger->info('Checking if image file exists', [
            'relative_path' => $relativePath,
            'exists' => $exists,
            'absolute_path' => $mediaDirectory->getAbsolutePath($relativePath)
        ]);

        return $exists;
    }

    /**
     * Get image file information
     *
     * @param string $relativePath
     * @return array
     */
    private function getImageFileInfo(string $relativePath): array
    {
        $mediaDirectory = $this->filesystem->getDirectoryRead(DirectoryList::MEDIA);
        $stat = $mediaDirectory->stat($relativePath);
        $extension = strtolower(pathinfo($relativePath, PATHINFO_EXTENSION));

        return [
            'size' => $stat['size'] ?? 0,
            'mime_type' => self::MIME_TYPES[$extension] ?? 'image/jpeg'
        ];
    }
}
